-- <PERSON><PERSON> Habits Hub - Complete Database Setup Script
-- This script sets up the entire database schema for the application
-- Run this in your Supabase SQL editor or via CLI

-- First, ensure we have the required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the main database schema by running files in order:
-- 1. Users table with gaming features (01_users_table.sql)
-- 2. Productivity tables (02_productivity_tables.sql) 
-- 3. Gaming tables (03_gaming_tables.sql)
-- 4. Fun activities tables (04_fun_activities_tables.sql)

-- This file serves as documentation for the setup process
-- The actual table creation is in the numbered SQL files

-- Database Overview:
-- =================

-- USERS & AUTHENTICATION:
-- - users: Main user profiles with gaming features (level, XP, streak, coins)

-- PRODUCTIVITY FEATURES (8 tables):
-- - notes: Personal and work notes with folders and tags
-- - tasks: Task management with priorities and recurring options
-- - content_pieces: Content creation and management
-- - calendar_events: Calendar integration and event management
-- - contacts: Contact management with folders
-- - diary_entries: Daily diary with mood tracking
-- - health_entries: Health and fitness tracking
-- - favorite_websites: Bookmark management

-- GAMING FEATURES (8 tables):
-- - achievements: Badge/trophy system
-- - quests: Daily/weekly/monthly challenges
-- - companions: Virtual pets/companions
-- - recipes: Recipe collection and management
-- - pictures: Photo gallery with organization
-- - xp_transactions: XP history and tracking
-- - daily_checkins: Daily login rewards
-- - mystery_box_openings: Reward system

-- FUN ACTIVITIES (12 tables):
-- - facts_quotes: Daily facts, quotes, and tips
-- - excuses: Excuse generator for various situations
-- - stories: Creative writing and story collection
-- - games: Game library and high scores
-- - game_sessions: Individual game session tracking
-- - news_sources: News feed configuration
-- - news_articles: News article storage and reading status
-- - email_templates: Email template management
-- - email_drafts: Email composition and scheduling
-- - user_preferences: User settings and preferences
-- - ai_conversations: AI assistant chat history
-- - shared_content: Content sharing system

-- TOTAL: 29 tables providing comprehensive functionality

-- Key Features:
-- =============
-- ✅ Row Level Security (RLS) on all tables
-- ✅ Automatic timestamp management (created_at, updated_at)
-- ✅ Soft deletes where appropriate (deleted_at)
-- ✅ Gaming system with XP, levels, achievements
-- ✅ Folder organization for most content types
-- ✅ Tag system for categorization
-- ✅ AI assistant integration
-- ✅ Content sharing capabilities
-- ✅ Performance indexes on key columns
-- ✅ Database functions for complex operations

-- Setup Instructions:
-- ==================
-- 1. Create a new Supabase project
-- 2. Run the SQL files in order:
--    a. 01_users_table.sql
--    b. 02_productivity_tables.sql
--    c. 03_gaming_tables.sql
--    d. 04_fun_activities_tables.sql
-- 3. Configure environment variables in your Next.js app
-- 4. Test the database connection and RLS policies

-- Environment Variables Needed:
-- =============================
-- NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
-- NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
-- SUPABASE_SERVICE_ROLE_KEY=your_service_role_key (for admin operations)

-- Authentication Setup:
-- =====================
-- Supabase Auth is configured to work with the users table
-- New user registration automatically creates a profile in the users table
-- Email confirmation can be enabled in Supabase dashboard
-- Social login providers can be configured as needed

-- RLS Security:
-- =============
-- All tables have Row Level Security enabled
-- Users can only access their own data
-- Shared content has special policies for public access via tokens
-- System-wide content (facts, quotes) can be accessed by all users

-- Performance Considerations:
-- ==========================
-- Indexes are created on frequently queried columns
-- JSONB columns are used for flexible data storage
-- Soft deletes prevent data loss while maintaining performance
-- Triggers automatically update timestamps

-- Backup and Maintenance:
-- ======================
-- Regular backups are handled by Supabase
-- Consider archiving old deleted records periodically
-- Monitor query performance and add indexes as needed
-- Update RLS policies as features evolve

-- Next Steps After Setup:
-- =======================
-- 1. Test user registration and authentication
-- 2. Verify RLS policies work correctly
-- 3. Test core CRUD operations for each table
-- 4. Set up real-time subscriptions for live updates
-- 5. Configure storage buckets for file uploads
-- 6. Set up database webhooks for external integrations
-- 7. Implement backup and monitoring strategies
