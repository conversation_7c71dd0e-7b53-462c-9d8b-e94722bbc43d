# 🔧 Google OAuth Fix Guide

## The Problem
You're getting `Error 400: redirect_uri_mismatch` because the redirect URI in your Google OAuth configuration doesn't match what Supabase expects.

## ✅ Step-by-Step Fix

### Step 1: Configure Google Cloud Console

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Select your project (or create one if needed)

2. **Enable Required APIs**
   - Go to **APIs & Services** → **Library**
   - Search and enable: **Google+ API** and **Google Identity API**

3. **Create OAuth 2.0 Credentials**
   - Go to **APIs & Services** → **Credentials**
   - Click **+ CREATE CREDENTIALS** → **OAuth 2.0 Client IDs**
   - Choose **Web application**
   - Give it a name (e.g., "Koi App")

4. **Configure Authorized Redirect URIs**
   - In **Authorized redirect URIs**, add this **EXACT** URL:
   ```
   https://arkywiwduoqpobflwssm.supabase.co/auth/v1/callback
   ```
   - **DO NOT** add localhost URLs
   - Click **Save**

5. **Copy Credentials**
   - Copy the **Client ID** and **Client Secret**

### Step 2: Configure Supabase

1. **Go to Supabase Dashboard**
   - Visit: https://app.supabase.com/project/arkywiwduoqpobflwssm

2. **Enable Google Provider**
   - Go to **Authentication** → **Providers**
   - Find **Google** and click **Enable**
   - Paste your **Client ID** and **Client Secret**
   - The **Redirect URL** should already be set to:
     ```
     https://arkywiwduoqpobflwssm.supabase.co/auth/v1/callback
     ```
   - Click **Save**

### Step 3: Configure Site URL (Important!)

1. **In Supabase Dashboard**
   - Go to **Authentication** → **URL Configuration**
   - Set **Site URL** to: `http://localhost:3000`
   - Add **Redirect URLs**:
     - `http://localhost:3000/auth/callback`
     - `http://localhost:3000/`
   - Click **Save**

### Step 4: Test the Fix

1. **Restart your development server**
   ```bash
   # Stop the server (Ctrl+C)
   npm run dev
   ```

2. **Clear browser cache**
   - Press **Ctrl+Shift+R** (hard refresh)
   - Or open an incognito/private window

3. **Test Google Sign-In**
   - Go to http://localhost:3000/auth
   - Click **Continue with Google**
   - Should work without redirect_uri_mismatch error

## 🔍 Troubleshooting

### If you still get redirect_uri_mismatch:
1. **Double-check the Google Console redirect URI** - it must be exactly:
   ```
   https://arkywiwduoqpobflwssm.supabase.co/auth/v1/callback
   ```

2. **Wait 5-10 minutes** - Google changes can take time to propagate

3. **Check Supabase logs**
   - Go to Supabase Dashboard → **Logs** → **Auth**
   - Look for any error messages

### If authentication succeeds but redirect fails:
1. **Check your Site URL** in Supabase Authentication settings
2. **Verify redirect URLs** include your localhost URLs

## 📝 Summary

The key points:
- ✅ Google OAuth redirect URI: `https://arkywiwduoqpobflwssm.supabase.co/auth/v1/callback`
- ✅ Supabase Site URL: `http://localhost:3000`
- ✅ Supabase Redirect URLs: Include your localhost callback
- ✅ Code already fixed to use proper callback handling

After following these steps, Google OAuth should work perfectly! 🎉
