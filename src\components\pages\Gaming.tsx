'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Zap, Trophy, Target, Gift, Award, Flame, Star, Calendar, CheckCircle } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { gamingService } from '../../lib/database';

interface GamingProps {
  onBack?: () => void;
}

interface UserProgress {
  current_level: number;
  total_xp: number;
  xp_to_next_level: number;
  daily_streak: number;
  longest_streak: number;
  achievements_unlocked: number;
  badges_earned: number;
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  xp_reward: number;
  rarity: string;
  earned_at?: string;
}

const Gaming = ({ onBack }: GamingProps) => {
  const { user } = useAuth();
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [canCheckIn, setCanCheckIn] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [checkingIn, setCheckingIn] = useState(false);

  useEffect(() => {
    if (user) {
      loadGamingData();
    }
  }, [user]);

  const loadGamingData = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Load user progress, achievements, etc.
      // For now, we'll use mock data since the full gaming system needs to be connected
      setUserProgress({
        current_level: 5,
        total_xp: 1250,
        xp_to_next_level: 250,
        daily_streak: 7,
        longest_streak: 15,
        achievements_unlocked: 8,
        badges_earned: 12
      });

      setAchievements([
        {
          id: '1',
          name: 'First Steps',
          description: 'Complete your first task',
          icon: '🎯',
          category: 'tasks',
          xp_reward: 50,
          rarity: 'common',
          earned_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          name: 'Note Taker',
          description: 'Create 10 notes',
          icon: '📝',
          category: 'notes',
          xp_reward: 100,
          rarity: 'rare',
          earned_at: '2024-01-20T15:30:00Z'
        },
        {
          id: '3',
          name: 'Streak Master',
          description: 'Maintain a 7-day streak',
          icon: '🔥',
          category: 'streaks',
          xp_reward: 200,
          rarity: 'epic',
          earned_at: '2024-01-25T09:15:00Z'
        }
      ]);

      // Check if user can check in today
      const lastCheckIn = localStorage.getItem(`last-checkin-${user.id}`);
      const today = new Date().toDateString();
      setCanCheckIn(lastCheckIn !== today);

    } catch (err) {
      setError('Failed to load gaming data');
    } finally {
      setLoading(false);
    }
  };

  const handleDailyCheckIn = async () => {
    if (!user || !canCheckIn) return;

    setCheckingIn(true);
    try {
      const { data, error } = await gamingService.performDailyCheckin(user.id);
      
      if (error) {
        setError(error.message);
      } else {
        // Update local state
        const today = new Date().toDateString();
        localStorage.setItem(`last-checkin-${user.id}`, today);
        setCanCheckIn(false);
        
        // Refresh gaming data
        await loadGamingData();
        
        // Show success message
        alert(`Daily check-in successful! You earned ${data?.xp_earned || 50} XP!`);
      }
    } catch (err) {
      setError('Failed to perform daily check-in');
    } finally {
      setCheckingIn(false);
    }
  };

  const getLevelProgress = () => {
    if (!userProgress) return 0;
    const currentLevelXP = userProgress.current_level * 100;
    const nextLevelXP = (userProgress.current_level + 1) * 100;
    const progressXP = userProgress.total_xp - currentLevelXP;
    const requiredXP = nextLevelXP - currentLevelXP;
    return (progressXP / requiredXP) * 100;
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-yellow-400 bg-yellow-400/20';
      case 'epic': return 'text-purple-400 bg-purple-400/20';
      case 'rare': return 'text-blue-400 bg-blue-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="text-center py-12">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-blue-200">Loading your gaming progress...</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
          <button 
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button 
              onClick={onBack}
              variant="outline" 
              className="border-blue-400/50 text-blue-200 hover:bg-blue-500/30"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-white">Gaming Hub</h1>
            <p className="text-blue-200">Track your progress, earn XP, and unlock achievements</p>
          </div>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Level Card */}
        <Card className="bg-gradient-to-br from-yellow-500/20 to-yellow-600/20 border border-yellow-400/30">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-400/30 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-yellow-300" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">Level {userProgress?.current_level}</h3>
              <p className="text-yellow-200 text-sm">{userProgress?.total_xp} XP Total</p>
            </div>
          </div>
          <div className="mb-2">
            <div className="flex justify-between text-sm text-yellow-200 mb-1">
              <span>Progress to Level {(userProgress?.current_level || 0) + 1}</span>
              <span>{userProgress?.xp_to_next_level} XP needed</span>
            </div>
            <div className="w-full bg-yellow-900/30 rounded-full h-2">
              <div 
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getLevelProgress()}%` }}
              ></div>
            </div>
          </div>
        </Card>

        {/* Streak Card */}
        <Card className="bg-gradient-to-br from-red-500/20 to-red-600/20 border border-red-400/30">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-red-400/30 rounded-full flex items-center justify-center">
              <Flame className="w-5 h-5 text-red-300" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">{userProgress?.daily_streak} Day Streak</h3>
              <p className="text-red-200 text-sm">Best: {userProgress?.longest_streak} days</p>
            </div>
          </div>
        </Card>

        {/* Achievements Card */}
        <Card className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 border border-purple-400/30">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-purple-400/30 rounded-full flex items-center justify-center">
              <Trophy className="w-5 h-5 text-purple-300" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">{userProgress?.achievements_unlocked} Achievements</h3>
              <p className="text-purple-200 text-sm">{userProgress?.badges_earned} badges earned</p>
            </div>
          </div>
        </Card>

        {/* Daily Check-in Card */}
        <Card className="bg-gradient-to-br from-green-500/20 to-green-600/20 border border-green-400/30">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-green-400/30 rounded-full flex items-center justify-center">
              <Calendar className="w-5 h-5 text-green-300" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">Daily Check-in</h3>
              <p className="text-green-200 text-sm">
                {canCheckIn ? 'Ready to check in!' : 'Already checked in today'}
              </p>
            </div>
          </div>
          <Button 
            onClick={handleDailyCheckIn}
            disabled={!canCheckIn || checkingIn}
            className={`w-full ${canCheckIn ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 cursor-not-allowed'}`}
          >
            {checkingIn ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : canCheckIn ? (
              <CheckCircle className="w-4 h-4 mr-2" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            {canCheckIn ? 'Check In' : 'Checked In'}
          </Button>
        </Card>
      </div>

      {/* Recent Achievements */}
      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">Recent Achievements</h3>
          <Button variant="outline" className="border-blue-400/50 text-blue-200 hover:bg-blue-500/30">
            View All
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {achievements.map(achievement => (
            <div 
              key={achievement.id}
              className={`p-4 rounded-lg border ${getRarityColor(achievement.rarity)}`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-2xl">{achievement.icon}</span>
                <div>
                  <h4 className="font-semibold text-white">{achievement.name}</h4>
                  <p className="text-xs text-blue-200 capitalize">{achievement.rarity}</p>
                </div>
              </div>
              <p className="text-sm text-blue-200 mb-2">{achievement.description}</p>
              <div className="flex justify-between items-center text-xs">
                <span className="text-yellow-300">+{achievement.xp_reward} XP</span>
                {achievement.earned_at && (
                  <span className="text-blue-300">
                    {new Date(achievement.earned_at).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Gaming Activities */}
      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-white mb-2">Gaming Activities</h3>
          <p className="text-blue-200">Explore different ways to earn XP and unlock achievements</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            { id: 'quests', name: 'Daily Quests', icon: Target, description: 'Complete daily challenges', color: 'text-yellow-400' },
            { id: 'rewards', name: 'Mystery Boxes', icon: Gift, description: 'Open surprise rewards', color: 'text-yellow-400' },
            { id: 'leaderboard', name: 'Leaderboard', icon: Trophy, description: 'Compete with others', color: 'text-yellow-400' },
            { id: 'badges', name: 'Badge Collection', icon: Award, description: 'Collect special badges', color: 'text-yellow-400' },
            { id: 'streaks', name: 'Streak Challenges', icon: Flame, description: 'Maintain activity streaks', color: 'text-yellow-400' },
            { id: 'level', name: 'Level Progress', icon: Star, description: 'Track your advancement', color: 'text-yellow-400' }
          ].map(activity => (
            <button
              key={activity.id}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <activity.icon className={`w-5 h-5 ${activity.color} drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]`} />
                </div>
                <h4 className="text-base font-semibold text-white">{activity.name}</h4>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{activity.description}</p>
            </button>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default Gaming;
