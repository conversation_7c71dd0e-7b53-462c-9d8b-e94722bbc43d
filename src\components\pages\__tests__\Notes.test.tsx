import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider } from '../../../contexts/AuthContext'
import Notes from '../Notes'
import * as supabaseModule from '../../../lib/supabase'

// Mock Supabase functions
jest.mock('../../../lib/supabase', () => ({
  createNote: jest.fn(),
  getNotes: jest.fn(),
  updateNote: jest.fn(),
  deleteNote: jest.fn(),
  awardXP: jest.fn(),
}))

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {},
}

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    loading: false,
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    signInWithGoogle: jest.fn(),
  }

  return (
    <AuthProvider value={mockAuthValue as any}>
      {children}
    </AuthProvider>
  )
}

const mockNotes = [
  {
    id: '1',
    title: 'Test Note 1',
    content: 'This is test content',
    folder: 'Personal',
    tags: ['test', 'example'],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'test-user-id',
  },
  {
    id: '2',
    title: 'Test Note 2',
    content: 'Another test note',
    folder: 'Work',
    tags: ['work'],
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    user_id: 'test-user-id',
  },
]

describe('Notes Component', () => {
  const mockOnBack = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock successful notes fetch
    ;(supabaseModule.getNotes as jest.Mock).mockResolvedValue({
      data: mockNotes,
      error: null,
    })
  })

  it('renders notes page with header', async () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Notes')).toBeInTheDocument()
    expect(screen.getByText('Create, organize, and manage your notes with AI assistance')).toBeInTheDocument()
    expect(screen.getByText('Back')).toBeInTheDocument()
  })

  it('loads and displays notes', async () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
      expect(screen.getByText('Test Note 2')).toBeInTheDocument()
    })
  })

  it('displays loading state initially', () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Loading your notes...')).toBeInTheDocument()
  })

  it('shows empty state when no notes exist', async () => {
    ;(supabaseModule.getNotes as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    })

    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('No notes yet')).toBeInTheDocument()
      expect(screen.getByText('Create your first note to get started on your productivity journey!')).toBeInTheDocument()
    })
  })

  it('handles search functionality', async () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search notes...')
    await userEvent.type(searchInput, 'Test Note 1')

    // Should filter notes based on search
    expect(screen.getByText('Test Note 1')).toBeInTheDocument()
  })

  it('creates new note when button is clicked', async () => {
    ;(supabaseModule.createNote as jest.Mock).mockResolvedValue({
      data: {
        id: '3',
        title: 'New Note',
        content: '',
        folder: 'Personal',
        tags: [],
        created_at: '2024-01-03T00:00:00Z',
        updated_at: '2024-01-03T00:00:00Z',
        user_id: 'test-user-id',
      },
      error: null,
    })

    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
    })

    const newNoteButton = screen.getByText('New Note')
    fireEvent.click(newNoteButton)

    await waitFor(() => {
      expect(supabaseModule.createNote).toHaveBeenCalled()
      expect(supabaseModule.awardXP).toHaveBeenCalledWith(
        'test-user-id',
        25,
        'Created a note',
        'productivity',
        expect.any(String),
        'note'
      )
    })
  })

  it('creates note from template', async () => {
    ;(supabaseModule.createNote as jest.Mock).mockResolvedValue({
      data: {
        id: '3',
        title: 'Cornell Notes Note',
        content: '## Notes\n\n\n## Cue\n\n\n## Summary\n\n',
        folder: 'Personal',
        tags: ['cornell_notes'],
        created_at: '2024-01-03T00:00:00Z',
        updated_at: '2024-01-03T00:00:00Z',
        user_id: 'test-user-id',
      },
      error: null,
    })

    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
    })

    const cornellTemplate = screen.getByText('Cornell Notes')
    fireEvent.click(cornellTemplate)

    await waitFor(() => {
      expect(supabaseModule.createNote).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Cornell Notes Note',
          content: '## Notes\n\n\n## Cue\n\n\n## Summary\n\n',
          tags: ['cornell_notes'],
        })
      )
    })
  })

  it('handles note deletion', async () => {
    ;(supabaseModule.deleteNote as jest.Mock).mockResolvedValue({
      data: null,
      error: null,
    })

    // Mock window.confirm
    window.confirm = jest.fn(() => true)

    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
    })

    const deleteButtons = screen.getAllByRole('button')
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg')?.getAttribute('data-lucide') === 'trash-2'
    )

    if (deleteButton) {
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(supabaseModule.deleteNote).toHaveBeenCalledWith('1')
      })
    }
  })

  it('calls onBack when back button is clicked', () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    expect(mockOnBack).toHaveBeenCalled()
  })

  it('handles API errors gracefully', async () => {
    ;(supabaseModule.getNotes as jest.Mock).mockResolvedValue({
      data: null,
      error: { message: 'Failed to fetch notes' },
    })

    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch notes')).toBeInTheDocument()
    })
  })

  it('filters notes by folder', async () => {
    render(
      <MockAuthProvider>
        <Notes onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Note 1')).toBeInTheDocument()
      expect(screen.getByText('Test Note 2')).toBeInTheDocument()
    })

    const folderSelect = screen.getByDisplayValue('All Folders')
    await userEvent.selectOptions(folderSelect, 'personal')

    // Should show only Personal folder notes
    expect(screen.getByText('Test Note 1')).toBeInTheDocument()
  })
})
