import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider } from '../../../contexts/AuthContext'
import Tasks from '../Tasks'
import * as databaseModule from '../../../lib/database'
import * as supabaseModule from '../../../lib/supabase'

// Mock database and supabase functions
jest.mock('../../../lib/database', () => ({
  tasksService: {
    getAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
}))

jest.mock('../../../lib/supabase', () => ({
  awardXP: jest.fn(),
  completeTask: jest.fn(),
}))

const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {},
}

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    loading: false,
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    signInWithGoogle: jest.fn(),
  }

  return (
    <AuthProvider value={mockAuthValue as any}>
      {children}
    </AuthProvider>
  )
}

const mockTasks = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'This is a test task',
    completed: false,
    folder: 'Personal',
    tags: ['test'],
    priority: 'high' as const,
    due_date: '2024-12-31',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'test-user-id',
  },
  {
    id: '2',
    title: 'Completed Task',
    description: 'This task is done',
    completed: true,
    completed_at: '2024-01-02T00:00:00Z',
    folder: 'Work',
    tags: ['work'],
    priority: 'medium' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    user_id: 'test-user-id',
  },
]

describe('Tasks Component', () => {
  const mockOnBack = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock successful tasks fetch
    ;(databaseModule.tasksService.getAll as jest.Mock).mockResolvedValue({
      data: mockTasks,
      error: null,
    })
  })

  it('renders tasks page with header', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Tasks')).toBeInTheDocument()
    expect(screen.getByText('Manage your tasks with priorities, due dates, and gamification')).toBeInTheDocument()
    expect(screen.getByText('Back')).toBeInTheDocument()
  })

  it('loads and displays tasks', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
      expect(screen.getByText('Completed Task')).toBeInTheDocument()
    })
  })

  it('displays loading state initially', () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    expect(screen.getByText('Loading your tasks...')).toBeInTheDocument()
  })

  it('shows empty state when no tasks exist', async () => {
    ;(databaseModule.tasksService.getAll as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    })

    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('No tasks yet')).toBeInTheDocument()
      expect(screen.getByText('Create your first task to start being productive!')).toBeInTheDocument()
    })
  })

  it('creates new task when button is clicked', async () => {
    ;(databaseModule.tasksService.create as jest.Mock).mockResolvedValue({
      data: {
        id: '3',
        title: 'New Task',
        description: '',
        completed: false,
        folder: 'Personal',
        tags: [],
        priority: 'medium',
        created_at: '2024-01-03T00:00:00Z',
        updated_at: '2024-01-03T00:00:00Z',
        user_id: 'test-user-id',
      },
      error: null,
    })

    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    const newTaskButton = screen.getByText('New Task')
    fireEvent.click(newTaskButton)

    await waitFor(() => {
      expect(databaseModule.tasksService.create).toHaveBeenCalled()
      expect(supabaseModule.awardXP).toHaveBeenCalledWith(
        'test-user-id',
        25,
        'Created a task',
        'productivity',
        expect.any(String),
        'task'
      )
    })
  })

  it('completes task when checkbox is clicked', async () => {
    ;(supabaseModule.completeTask as jest.Mock).mockResolvedValue({
      data: { xp_earned: 15 },
      error: null,
    })

    // Mock window.alert
    window.alert = jest.fn()

    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    const checkboxes = screen.getAllByRole('button')
    const taskCheckbox = checkboxes.find(button => 
      button.closest('div')?.textContent?.includes('Test Task 1')
    )

    if (taskCheckbox) {
      fireEvent.click(taskCheckbox)
      
      await waitFor(() => {
        expect(supabaseModule.completeTask).toHaveBeenCalledWith('1')
        expect(window.alert).toHaveBeenCalledWith('Task completed! You earned 15 XP!')
      })
    }
  })

  it('handles task deletion with confirmation', async () => {
    ;(databaseModule.tasksService.delete as jest.Mock).mockResolvedValue({
      data: null,
      error: null,
    })

    // Mock window.confirm
    window.confirm = jest.fn(() => true)

    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    const deleteButtons = screen.getAllByRole('button')
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg')?.getAttribute('data-lucide') === 'trash-2'
    )

    if (deleteButton) {
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this task?')
        expect(databaseModule.tasksService.delete).toHaveBeenCalledWith('1', false)
      })
    }
  })

  it('filters tasks by priority', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
      expect(screen.getByText('Completed Task')).toBeInTheDocument()
    })

    const prioritySelect = screen.getByDisplayValue('All Priorities')
    await userEvent.selectOptions(prioritySelect, 'high')

    // Should show only high priority tasks
    expect(screen.getByText('Test Task 1')).toBeInTheDocument()
  })

  it('filters tasks by completion status', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    const showCompletedCheckbox = screen.getByLabelText('Show Completed')
    await userEvent.click(showCompletedCheckbox)

    // Should show completed tasks
    expect(screen.getByText('Completed Task')).toBeInTheDocument()
  })

  it('searches tasks by title and description', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search tasks...')
    await userEvent.type(searchInput, 'Test Task 1')

    // Should filter tasks based on search
    expect(screen.getByText('Test Task 1')).toBeInTheDocument()
  })

  it('displays priority indicators correctly', async () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
    })

    // Check for priority indicators
    expect(screen.getByText('High')).toBeInTheDocument()
    expect(screen.getByText('Medium')).toBeInTheDocument()
  })

  it('calls onBack when back button is clicked', () => {
    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    expect(mockOnBack).toHaveBeenCalled()
  })

  it('handles API errors gracefully', async () => {
    ;(databaseModule.tasksService.getAll as jest.Mock).mockResolvedValue({
      data: null,
      error: { message: 'Failed to fetch tasks' },
    })

    render(
      <MockAuthProvider>
        <Tasks onBack={mockOnBack} />
      </MockAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch tasks')).toBeInTheDocument()
    })
  })
})
