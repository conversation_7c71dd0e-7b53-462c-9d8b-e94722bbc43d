'use client';

import {
  <PERSON>yNote,
  CheckSquare,
  Calendar,
  Lightbulb,
  Smile,
  Users2,
  Trophy,
  ChefHat,
  Gamepad2,
  Zap,
  Gift,
  Target,
  Clock,
  Mail,
  Phone,
  BookOpen,
  Heart,
  Globe,
  Quote,
  Utensils,
  Camera,
  BookText,
  Newspaper,
  Share,
  Award,
  Eye,
  Flame
} from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { useAuth } from '../../contexts/AuthContext';
import { useState, useEffect } from 'react';

interface DashboardProps {
  language?: string;
  onNavigate?: (section: string) => void;
}

interface UserProgress {
  current_level: number;
  total_xp: number;
  xp_to_next_level: number;
  daily_streak: number;
}

const Dashboard = ({ language = 'en', onNavigate }: DashboardProps) => {
  const { t } = useTranslation(language as any);
  const { user } = useAuth();
  const [userProgress, setUserProgress] = useState<UserProgress>({
    current_level: 1,
    total_xp: 0,
    xp_to_next_level: 100,
    daily_streak: 0
  });

  // Load user progress
  useEffect(() => {
    if (user) {
      // For now, use mock data. In a real app, this would fetch from the database
      setUserProgress({
        current_level: 5,
        total_xp: 1250,
        xp_to_next_level: 250,
        daily_streak: 7
      });
    }
  }, [user]);

  const getLevelProgress = () => {
    const currentLevelXP = userProgress.current_level * 100;
    const nextLevelXP = (userProgress.current_level + 1) * 100;
    const progressXP = userProgress.total_xp - currentLevelXP;
    const requiredXP = nextLevelXP - currentLevelXP;
    return Math.max(0, Math.min(100, (progressXP / requiredXP) * 100));
  };

  const productivityFeatures = [
    { id: 'notes', name: t('notes'), icon: StickyNote, description: t('notesDesc') },
    { id: 'tasks', name: t('tasks'), icon: CheckSquare, description: t('tasksDesc') },
    { id: 'calendar', name: t('calendar'), icon: Calendar, description: t('calendarDesc') },
    { id: 'email', name: t('email'), icon: Mail, description: t('emailDesc') },
    { id: 'contacts', name: t('contacts'), icon: Phone, description: t('contactsDesc') },
    { id: 'diary', name: t('diary'), icon: BookOpen, description: t('diaryDesc') },
    { id: 'health', name: t('health'), icon: Heart, description: t('healthDesc') },
    { id: 'content', name: t('content'), icon: Lightbulb, description: t('contentDesc') },
    { id: 'websites', name: t('websites'), icon: Globe, description: t('websitesDesc') },
  ];

  const funFeatures = [
    { id: 'facts', name: 'Fun Facts, Jokes, Quotes & Riddles', icon: Quote, description: 'Enjoy fun facts, jokes, quotes and riddles', color: 'text-pink-400' },
    { id: 'excuses', name: 'Excuses', icon: Smile, description: 'Generate customized excuses', color: 'text-purple-400' },
    { id: 'companions', name: 'Companions', icon: Users2, description: 'Manage your virtual companions', color: 'text-pink-400' },
    { id: 'achievements', name: 'My Achievements', icon: Trophy, description: 'View your badges, awards and trophies', color: 'text-pink-400' },
    { id: 'recipes', name: 'My Recipes', icon: Utensils, description: 'Create and organize your favorite recipes', color: 'text-purple-400' },
    { id: 'pictures', name: 'My Pictures', icon: Camera, description: 'Upload and collect your favorite pictures', color: 'text-pink-400' },
    { id: 'stories', name: 'Koi Adventure Stories', icon: BookText, description: 'Dive into interactive and imaginative Koi stories', color: 'text-pink-400' },
    { id: 'games', name: 'Mini-Games', icon: Gamepad2, description: 'Play quick and fun mini-games to boost your mood', color: 'text-purple-400' },
    { id: 'news', name: 'Latest News', icon: Newspaper, description: 'Catch up on the latest, family-friendly news', color: 'text-pink-400' },
  ];

  const gamingFeatures = [
    { id: 'level', name: t('levelUp'), icon: Zap, description: t('levelUpDesc') },
    { id: 'quests', name: t('dailyQuests'), icon: Target, description: t('dailyQuestsDesc') },
    { id: 'rewards', name: t('rewards'), icon: Gift, description: t('rewardsDesc') },
    { id: 'leaderboard', name: t('leaderboard'), icon: Trophy, description: t('leaderboardDesc') },
    { id: 'badges', name: t('badges'), icon: Award, description: t('badgesDesc') },
    { id: 'streaks', name: t('streaks'), icon: Flame, description: t('streaksDesc') },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-4">
          {t('welcomeBack')}
        </h1>
        <p className="text-blue-200 text-lg">
          {t('readyToday')}
        </p>
      </div>

      {/* Dashboard Cards Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Your Progress Card */}
        <div
          className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg cursor-pointer hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
          onClick={() => onNavigate?.('level')}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">{t('yourProgress')}</h3>
            <Share className="w-5 h-5 text-blue-200" />
          </div>
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-yellow-900" />
            </div>
            <div>
              <p className="text-xl font-bold">{t('level')} {userProgress.current_level}</p>
              <p className="text-blue-200 text-sm">{userProgress.total_xp} XP Total</p>
            </div>
          </div>
          <div className="mb-2">
            <div className="flex justify-between text-sm mb-1">
              <span>Progress to Level {userProgress.current_level + 1}</span>
              <span>{userProgress.xp_to_next_level} XP needed</span>
            </div>
            <div className="w-full bg-blue-400 rounded-full h-2">
              <div
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${getLevelProgress()}%` }}
              ></div>
            </div>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center">
              <Flame className="w-4 h-4 mr-1 text-red-400" />
              {userProgress.daily_streak} day streak
            </span>
            <span className="text-blue-200">Click to view details</span>
          </div>
        </div>

        {/* How are you feeling Card */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-lg font-semibold mb-4">{t('howAreYouFeeling')}</h3>
          <div className="grid grid-cols-3 gap-3">
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😊</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😐</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😔</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😴</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">🤔</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😎</span>
            </button>
          </div>
        </div>

        {/* Daily Inspiration Card */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-lg font-semibold mb-4">{t('dailyInspiration')}</h3>
          <div className="text-center">
            <Quote className="w-8 h-8 text-blue-200 mx-auto mb-3" />
            <p className="text-sm italic mb-3">
              "The journey of a thousand miles begins with one step."
            </p>
            <p className="text-xs text-blue-200">- Lao Tzu</p>
          </div>
        </div>
      </div>

      {/* Productivity Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">{t('productivityActivities')}</h2>
          <p className="text-blue-200 text-sm">{t('productivityDescription')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {productivityFeatures.map((feature) => (
            <button
              key={feature.id}
              onClick={() => onNavigate?.(feature.id)}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Fun Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">{t('funActivities')}</h2>
          <p className="text-blue-200 text-sm">{t('funDescription')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {funFeatures.map((feature) => (
            <button
              key={feature.id}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Gaming Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">{t('gamingActivities')}</h2>
          <p className="text-blue-200 text-sm">{t('gamingDescription')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {gamingFeatures.map((feature) => (
            <button
              key={feature.id}
              onClick={() => onNavigate?.(feature.id)}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>



      {/* Daily Progress Visualization */}
      <div className="mb-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">My Daily Progress Visualization</h2>
          <p className="text-blue-200">Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!</p>
        </div>

        <div className="bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden">
          {/* Sky and Sun */}
          <div className="absolute top-4 right-8">
            <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl">☀️</span>
            </div>
          </div>

          {/* Clouds */}
          <div className="absolute top-8 left-12">
            <div className="w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center">
              <span className="text-sm">😊</span>
            </div>
          </div>
          <div className="absolute top-12 left-32">
            <div className="w-10 h-6 bg-white rounded-full opacity-60"></div>
          </div>

          {/* Island Scene */}
          <div className="relative z-10 text-center pt-16">
            <div className="inline-block relative">
              {/* Island Base */}
              <div className="w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative">
                {/* Island Top */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full">
                  {/* Palm Tree */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4">
                    <div className="w-2 h-8 bg-amber-600"></div>
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      <span className="text-2xl">🌴</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-lg font-semibold mb-2">Island</p>
              <div className="max-w-md mx-auto">
                <div className="flex justify-between text-sm mb-2">
                  <span>Island Growth: 0%</span>
                </div>
                <div className="w-full bg-blue-300 rounded-full h-3">
                  <div className="bg-green-400 h-3 rounded-full" style={{ width: '0%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
