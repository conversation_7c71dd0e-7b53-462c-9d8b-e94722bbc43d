"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/pages/Auth.tsx":
/*!***************************************!*\
  !*** ./src/components/pages/Auth.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(app-pages-browser)/./src/lib/i18n/useTranslation.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Auth = (param)=>{\n    let { language = 'en', onLanguageChange } = param;\n    _s();\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(language);\n    const { signUp, signIn, signInWithMagicLink, signInWithGoogle } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showLanguageDropdown, setShowLanguageDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'en',\n            label: 'US English',\n            flag: '🇺🇸'\n        },\n        {\n            code: 'es',\n            label: 'ES Español',\n            flag: '🇪🇸'\n        },\n        {\n            code: 'fr',\n            label: 'FR Français',\n            flag: '🇫🇷'\n        },\n        {\n            code: 'de',\n            label: 'DE Deutsch',\n            flag: '🇩🇪'\n        },\n        {\n            code: 'it',\n            label: 'IT Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'pt',\n            label: 'PT Português',\n            flag: '🇵🇹'\n        },\n        {\n            code: 'nl',\n            label: 'NL Nederlands',\n            flag: '🇳🇱'\n        },\n        {\n            code: 'zh',\n            label: 'CN 中文',\n            flag: '🇨🇳'\n        },\n        {\n            code: 'ja',\n            label: 'JP 日本語',\n            flag: '🇯🇵'\n        },\n        {\n            code: 'ko',\n            label: 'KR 한국어',\n            flag: '🇰🇷'\n        },\n        {\n            code: 'ru',\n            label: 'RU Русский',\n            flag: '🇷🇺'\n        },\n        {\n            code: 'gr',\n            label: 'GR Ελληνικά',\n            flag: '🇬🇷'\n        }\n    ];\n    const currentLang = languages.find((lang)=>lang.code === language) || languages[0];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setMessage('');\n        try {\n            let result;\n            if (authMode === 'signup') {\n                result = await signUp(email, password);\n                if (!result.error) {\n                    setMessage('Check your email for verification link!');\n                }\n            } else if (authMode === 'signin') {\n                result = await signIn(email, password);\n            } else if (authMode === 'magic') {\n                result = await signInWithMagicLink(email);\n                if (!result.error) {\n                    setMessage('Check your email for the magic link!');\n                }\n            }\n            if (result === null || result === void 0 ? void 0 : result.error) {\n                setMessage(result.error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await signInWithGoogle();\n            if (error) {\n                setMessage(error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-2 overflow-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-sm my-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-blue-200/30 via-blue-300/40 to-blue-400/30 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-1 animate-bounce border-2 border-white/20 backdrop-blur-sm\",\n                                    style: {\n                                        animationDuration: '3s'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"64\",\n                                        height: \"64\",\n                                        viewBox: \"0 0 240 240\",\n                                        className: \"drop-shadow-lg\",\n                                        style: {\n                                            filter: 'none'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"120\",\n                                                cy: \"120\",\n                                                rx: \"50\",\n                                                ry: \"45\",\n                                                fill: \"url(#koiBodyUltimate)\",\n                                                stroke: \"url(#koiStrokeUltimate)\",\n                                                strokeWidth: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M70 120 Q35 95 20 70 Q25 80 40 95 Q55 110 70 115 Q55 125 40 140 Q25 155 20 165 Q35 140 70 120\",\n                                                fill: \"url(#tailUltimate)\",\n                                                stroke: \"url(#koiStrokeUltimate)\",\n                                                strokeWidth: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M100 75 Q95 55 105 60 Q115 65 125 60 Q135 55 130 75 Q120 80 110 77 Q105 80 100 75\",\n                                                fill: \"url(#finUltimate)\",\n                                                stroke: \"url(#koiStrokeUltimate)\",\n                                                strokeWidth: \"1.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"90\",\n                                                cy: \"155\",\n                                                rx: \"15\",\n                                                ry: \"10\",\n                                                fill: \"url(#finUltimate)\",\n                                                stroke: \"url(#koiStrokeUltimate)\",\n                                                strokeWidth: \"1.5\",\n                                                transform: \"rotate(25 90 155)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"150\",\n                                                cy: \"155\",\n                                                rx: \"15\",\n                                                ry: \"10\",\n                                                fill: \"url(#finUltimate)\",\n                                                stroke: \"url(#koiStrokeUltimate)\",\n                                                strokeWidth: \"1.5\",\n                                                transform: \"rotate(-25 150 155)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"140\",\n                                                cy: \"105\",\n                                                r: \"9\",\n                                                fill: \"white\",\n                                                stroke: \"#000\",\n                                                strokeWidth: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"140\",\n                                                cy: \"105\",\n                                                r: \"7\",\n                                                fill: \"#000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"142\",\n                                                cy: \"102\",\n                                                r: \"2.5\",\n                                                fill: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"155\",\n                                                cy: \"120\",\n                                                rx: \"7\",\n                                                ry: \"5\",\n                                                fill: \"#FF69B4\",\n                                                opacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M150 130 Q155 135 150 140\",\n                                                stroke: \"#000\",\n                                                strokeWidth: \"3\",\n                                                fill: \"none\",\n                                                strokeLinecap: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"100\",\n                                                cy: \"90\",\n                                                rx: \"5\",\n                                                ry: \"4\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"115\",\n                                                cy: \"85\",\n                                                rx: \"4\",\n                                                ry: \"3\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"95\",\n                                                cy: \"110\",\n                                                rx: \"4\",\n                                                ry: \"3\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"110\",\n                                                cy: \"115\",\n                                                rx: \"4\",\n                                                ry: \"3\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"125\",\n                                                cy: \"100\",\n                                                rx: \"4\",\n                                                ry: \"3\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"130\",\n                                                cy: \"115\",\n                                                rx: \"4\",\n                                                ry: \"3\",\n                                                fill: \"#FFFF00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"190\",\n                                                cy: \"70\",\n                                                r: \"3.5\",\n                                                fill: \"rgba(255,255,255,0.8)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"200\",\n                                                cy: \"95\",\n                                                r: \"2.5\",\n                                                fill: \"rgba(255,255,255,0.7)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"195\",\n                                                cy: \"60\",\n                                                r: \"2\",\n                                                fill: \"rgba(255,255,255,0.9)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"185\",\n                                                cy: \"170\",\n                                                r: \"3\",\n                                                fill: \"rgba(255,255,255,0.6)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"195\",\n                                                cy: \"180\",\n                                                r: \"2.2\",\n                                                fill: \"rgba(255,255,255,0.7)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                                                        id: \"koiBodyUltimate\",\n                                                        cx: \"0.3\",\n                                                        cy: \"0.3\",\n                                                        r: \"0.8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#00FFFF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"20%\",\n                                                                stopColor: \"#40E0D0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"40%\",\n                                                                stopColor: \"#20B2AA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"60%\",\n                                                                stopColor: \"#4169E1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"80%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"tailUltimate\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF69B4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"15%\",\n                                                                stopColor: \"#FF1493\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"30%\",\n                                                                stopColor: \"#DA70D6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"70%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"85%\",\n                                                                stopColor: \"#9400D3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8B008B\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"finUltimate\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF69B4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"25%\",\n                                                                stopColor: \"#DA70D6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"75%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#9400D3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"koiStrokeUltimate\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF1493\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-bold text-white mb-0.5\",\n                                children: \"Welcome to Koi App\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-xs\",\n                                children: \"Your personal productivity assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 backdrop-blur-lg rounded-2xl p-3 shadow-2xl border border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLanguageDropdown(!showLanguageDropdown),\n                                        className: \"w-full flex items-center justify-center space-x-2 bg-blue-400/30 hover:bg-blue-400/50 px-2 py-1.5 rounded-lg transition-colors text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: [\n                                                    currentLang.flag,\n                                                    \" \",\n                                                    currentLang.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full left-0 right-0 mt-2 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-60 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2\",\n                                            children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        onLanguageChange === null || onLanguageChange === void 0 ? void 0 : onLanguageChange(lang.code);\n                                                        setShowLanguageDropdown(false);\n                                                    },\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors \".concat(language === lang.code ? 'bg-blue-400/50 text-white' : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: lang.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: lang.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, lang.code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-blue-600/50 rounded-xl p-1 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signin'),\n                                        className: \"flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all \".concat(authMode === 'signin' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signup'),\n                                        className: \"flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all \".concat(authMode === 'signup' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('magic'),\n                                        className: \"flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all \".concat(authMode === 'magic' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Magic Link\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-blue-200 text-xs font-medium mb-0.5\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-9 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    authMode !== 'magic' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-0.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-blue-200 text-xs font-medium\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    authMode === 'signin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"text-blue-300 text-xs hover:text-white transition-colors\",\n                                                        children: \"Forgot?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: showPassword ? 'text' : 'password',\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-9 pr-9 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute right-2.5 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-3.5 h-3.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 37\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-3.5 h-3.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 74\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3.5 h-3.5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: authMode === 'signin' ? 'Sign In' : authMode === 'signup' ? 'Sign Up' : 'Send Magic Link'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full border-t border-blue-400/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex justify-center text-xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 bg-blue-600/80 text-blue-200\",\n                                                    children: \"or\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGoogleSignIn,\n                                        disabled: loading,\n                                        className: \"w-full mt-2 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#4285F4\",\n                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#34A853\",\n                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#FBBC05\",\n                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#EA4335\",\n                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Continue with Google\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, undefined),\n                            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1.5 p-1.5 rounded-lg text-xs \".concat(message.includes('Check your email') ? 'bg-green-500/20 text-green-200 border border-green-400/30' : 'bg-red-500/20 text-red-200 border border-red-400/30'),\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setShowLanguageDropdown(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Auth, \"UjxC3D3aqiHGTxaSKNO3bpH+I80=\", false, function() {\n    return [\n        _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Auth;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Auth);\nvar _c;\n$RefreshReg$(_c, \"Auth\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Auth.tsx\n"));

/***/ })

});