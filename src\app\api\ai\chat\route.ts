import { NextRequest, NextResponse } from 'next/server';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

export async function POST(request: NextRequest) {
  try {
    const { message, context, feature } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'DeepSeek API key not configured' },
        { status: 500 }
      );
    }

    // Create context-aware system prompt based on feature
    const systemPrompts = {
      notes: "You are a helpful AI assistant for note-taking. Help users organize thoughts, suggest note structures, and provide writing assistance. Be concise and practical.",
      tasks: "You are a productivity AI assistant for task management. Help users break down complex tasks, prioritize work, and suggest efficient workflows. Focus on actionable advice.",
      content: "You are a creative AI assistant for content creation. Help users brainstorm ideas, structure content, and improve their writing. Be creative and inspiring.",
      calendar: "You are a scheduling AI assistant. Help users organize their time, suggest meeting structures, and optimize their calendar. Be efficient and time-conscious.",
      health: "You are a wellness AI assistant. Provide general health and fitness guidance, suggest healthy habits, and help track wellness goals. Always recommend consulting healthcare professionals for medical advice.",
      recipes: "You are a culinary AI assistant. Help users with recipe suggestions, cooking tips, meal planning, and dietary considerations. Be practical and food-safety conscious.",
      general: "You are Koi, the friendly AI assistant for the Joyful Habits Hub. Help users with productivity, wellness, and personal growth in a positive, encouraging way."
    };

    const systemPrompt = systemPrompts[feature as keyof typeof systemPrompts] || systemPrompts.general;

    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          ...(context ? [{ role: 'user', content: `Context: ${context}` }] : []),
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
        stream: false
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('DeepSeek API error:', errorData);
      return NextResponse.json(
        { error: 'Failed to get AI response' },
        { status: response.status }
      );
    }

    const data = await response.json();
    const aiMessage = data.choices?.[0]?.message?.content;

    if (!aiMessage) {
      return NextResponse.json(
        { error: 'No response from AI' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: aiMessage,
      feature,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('AI chat error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
