'use client';

import { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { Eye, EyeOff, Mail, Lock, Globe, ChevronDown, Fingerprint } from 'lucide-react';

interface AuthProps {
  language?: string;
  onLanguageChange?: (language: string) => void;
}

const Auth = ({ language = 'en', onLanguageChange }: AuthProps) => {
  const { t } = useTranslation(language as any);
  const { signUp, signIn, signInWithMagicLink, signInWithGoogle } = useAuth();
  
  const [authMode, setAuthMode] = useState<'signin' | 'signup' | 'magic'>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  const languages = [
    { code: 'en', label: 'US English', flag: '🇺🇸' },
    { code: 'es', label: 'ES Español', flag: '🇪🇸' },
    { code: 'fr', label: 'FR Français', flag: '🇫🇷' },
    { code: 'de', label: 'DE Deutsch', flag: '🇩🇪' },
    { code: 'it', label: 'IT Italiano', flag: '🇮🇹' },
    { code: 'pt', label: 'PT Português', flag: '🇵🇹' },
    { code: 'nl', label: 'NL Nederlands', flag: '🇳🇱' },
    { code: 'zh', label: 'CN 中文', flag: '🇨🇳' },
    { code: 'ja', label: 'JP 日本語', flag: '🇯🇵' },
    { code: 'ko', label: 'KR 한국어', flag: '🇰🇷' },
    { code: 'ru', label: 'RU Русский', flag: '🇷🇺' },
    { code: 'gr', label: 'GR Ελληνικά', flag: '🇬🇷' },
  ];

  const currentLang = languages.find(lang => lang.code === language) || languages[0];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      let result;
      
      if (authMode === 'signup') {
        result = await signUp(email, password);
        if (!result.error) {
          setMessage('Check your email for verification link!');
        }
      } else if (authMode === 'signin') {
        result = await signIn(email, password);
      } else if (authMode === 'magic') {
        result = await signInWithMagicLink(email);
        if (!result.error) {
          setMessage('Check your email for the magic link!');
        }
      }

      if (result?.error) {
        setMessage(result.error.message);
      }
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const { error } = await signInWithGoogle();
      if (error) {
        setMessage(error.message);
      }
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-2 overflow-auto">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      </div>

      <div className="relative w-full max-w-sm my-4">
        {/* Animated Koi Logo - Single bubble with proper Koi fish */}
        <div className="text-center mb-2">
          <div className="relative inline-block">
            {/* Single bubble with Koi fish - slow bobbing animation */}
            <div className="w-12 h-12 bg-gradient-to-br from-pink-200 via-purple-300 to-blue-300 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-1 animate-bounce border-3 border-white/30" style={{ animationDuration: '3s' }}>
              {/* Cute Koi Fish SVG - Exact match to reference image */}
              <svg width="36" height="36" viewBox="0 0 120 100" className="drop-shadow-lg">
                {/* Main body - round and cute */}
                <ellipse cx="60" cy="50" rx="28" ry="22" fill="url(#koiBodyAuth)" stroke="url(#koiStrokeAuth)" strokeWidth="2"/>

                {/* Large tail fin - flowing and colorful */}
                <path d="M32 50 Q15 35 8 25 Q12 30 18 35 Q25 40 32 45 Q25 50 18 55 Q12 60 8 65 Q15 55 32 50"
                      fill="url(#tailGradientAuth)" stroke="url(#koiStrokeAuth)" strokeWidth="1.5"/>

                {/* Top dorsal fin */}
                <path d="M50 28 Q45 18 55 22 Q65 26 60 32 Q55 30 50 28"
                      fill="url(#finGradientAuth)" stroke="url(#koiStrokeAuth)" strokeWidth="1"/>

                {/* Side fins - small and cute */}
                <ellipse cx="45" cy="65" rx="10" ry="6" fill="url(#finGradientAuth)"
                         stroke="url(#koiStrokeAuth)" strokeWidth="1" transform="rotate(25 45 65)" />
                <ellipse cx="75" cy="65" rx="10" ry="6" fill="url(#finGradientAuth)"
                         stroke="url(#koiStrokeAuth)" strokeWidth="1" transform="rotate(-25 75 65)" />

                {/* Large cute eyes */}
                <circle cx="70" cy="42" r="6" fill="white" stroke="#333" strokeWidth="1"/>
                <circle cx="70" cy="42" r="4" fill="black" />
                <circle cx="71.5" cy="40.5" r="1.5" fill="white" />

                {/* Cute smile */}
                <path d="M82 50 Q85 53 82 56" stroke="#333" strokeWidth="2" fill="none" strokeLinecap="round"/>

                {/* Colorful spots - yellow/green like in reference */}
                <circle cx="50" cy="40" r="3" fill="#FFD700" opacity="0.9" />
                <circle cx="55" cy="35" r="2.5" fill="#90EE90" opacity="0.9" />
                <circle cx="45" cy="50" r="2" fill="#FFD700" opacity="0.9" />
                <circle cx="65" cy="55" r="2.5" fill="#90EE90" opacity="0.9" />
                <circle cx="58" cy="58" r="2" fill="#FFD700" opacity="0.9" />

                {/* Small bubbles around the fish */}
                <circle cx="95" cy="30" r="2" fill="rgba(255,255,255,0.6)" />
                <circle cx="100" cy="40" r="1.5" fill="rgba(255,255,255,0.5)" />
                <circle cx="98" cy="25" r="1" fill="rgba(255,255,255,0.7)" />

                {/* Gradients for the cute colorful look */}
                <defs>
                  <radialGradient id="koiBodyAuth" cx="0.3" cy="0.3">
                    <stop offset="0%" stopColor="#00FFFF" />
                    <stop offset="40%" stopColor="#40E0D0" />
                    <stop offset="70%" stopColor="#9370DB" />
                    <stop offset="100%" stopColor="#FF69B4" />
                  </radialGradient>
                  <linearGradient id="tailGradientAuth" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF69B4" />
                    <stop offset="30%" stopColor="#DA70D6" />
                    <stop offset="70%" stopColor="#9370DB" />
                    <stop offset="100%" stopColor="#8A2BE2" />
                  </linearGradient>
                  <linearGradient id="finGradientAuth" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FFB6C1" />
                    <stop offset="50%" stopColor="#DDA0DD" />
                    <stop offset="100%" stopColor="#9370DB" />
                  </linearGradient>
                  <linearGradient id="koiStrokeAuth" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF1493" />
                    <stop offset="100%" stopColor="#8A2BE2" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>

          <h1 className="text-lg font-bold text-white mb-0.5">
            Welcome to Koi App
          </h1>
          <p className="text-blue-200 text-xs">
            Your personal productivity assistant
          </p>
        </div>

        {/* Auth Card */}
        <div className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 backdrop-blur-lg rounded-2xl p-3 shadow-2xl border border-blue-400/30">
          {/* Language Selector */}
          <div className="relative mb-2">
            <button
              onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              className="w-full flex items-center justify-center space-x-2 bg-blue-400/30 hover:bg-blue-400/50 px-2 py-1.5 rounded-lg transition-colors text-white"
            >
              <Globe className="w-3 h-3" />
              <span className="text-xs font-medium">{currentLang.flag} {currentLang.label}</span>
              <ChevronDown className="w-3 h-3" />
            </button>

            {showLanguageDropdown && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-60 overflow-y-auto">
                <div className="p-2">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        onLanguageChange?.(lang.code);
                        setShowLanguageDropdown(false);
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        language === lang.code 
                          ? 'bg-blue-400/50 text-white' 
                          : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'
                      }`}
                    >
                      <span className="text-lg">{lang.flag}</span>
                      <span className="text-sm font-medium">{lang.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Auth Mode Tabs */}
          <div className="flex bg-blue-600/50 rounded-xl p-1 mb-2">
            <button
              onClick={() => setAuthMode('signin')}
              className={`flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all ${
                authMode === 'signin'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Sign In
            </button>
            <button
              onClick={() => setAuthMode('signup')}
              className={`flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all ${
                authMode === 'signup'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Sign Up
            </button>
            <button
              onClick={() => setAuthMode('magic')}
              className={`flex-1 py-1.5 px-1 rounded-lg text-xs font-semibold transition-all ${
                authMode === 'magic'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Magic Link
            </button>
          </div>

          {/* Auth Form */}
          <form onSubmit={handleSubmit} className="space-y-2">
            {/* Email Field */}
            <div>
              <label className="block text-blue-200 text-xs font-medium mb-0.5">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-blue-300" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-9 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm"
                  required
                />
              </div>
            </div>

            {/* Password Field (not shown for magic link) */}
            {authMode !== 'magic' && (
              <div>
                <div className="flex justify-between items-center mb-0.5">
                  <label className="text-blue-200 text-xs font-medium">
                    Password
                  </label>
                  {authMode === 'signin' && (
                    <button
                      type="button"
                      className="text-blue-300 text-xs hover:text-white transition-colors"
                    >
                      Forgot?
                    </button>
                  )}
                </div>
                <div className="relative">
                  <Lock className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-blue-300" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-9 pr-9 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="w-3.5 h-3.5" /> : <Eye className="w-3.5 h-3.5" />}
                  </button>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
            >
              {loading ? (
                <div className="w-3.5 h-3.5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  <span>
                    {authMode === 'signin' ? 'Sign In' :
                     authMode === 'signup' ? 'Sign Up' :
                     'Send Magic Link'}
                  </span>
                  <span>→</span>
                </>
              )}
            </button>
          </form>

          {/* Google OAuth Button */}
          <div className="mt-2">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-blue-400/30"></div>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="px-2 bg-blue-600/80 text-blue-200">or</span>
              </div>
            </div>

            <button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="w-full mt-2 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm"
            >
              <svg className="w-4 h-4" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Continue with Google</span>
            </button>
          </div>

          {/* Message */}
          {message && (
            <div className={`mt-1.5 p-1.5 rounded-lg text-xs ${
              message.includes('Check your email')
                ? 'bg-green-500/20 text-green-200 border border-green-400/30'
                : 'bg-red-500/20 text-red-200 border border-red-400/30'
            }`}>
              {message}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showLanguageDropdown && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowLanguageDropdown(false)}
        />
      )}
    </div>
  );
};

export default Auth;
