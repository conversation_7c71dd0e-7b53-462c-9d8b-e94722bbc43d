'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabase/client';

export default function AuthCallback() {
  const router = useRouter();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Handle the OAuth callback by exchanging the code for a session
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          router.push('/auth?error=' + encodeURIComponent(error.message));
          return;
        }

        if (data.session) {
          // Successfully authenticated, redirect to dashboard
          router.push('/');
        } else {
          // No session, redirect to auth page
          router.push('/auth');
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error);
        router.push('/auth?error=unexpected_error');
      }
    };

    handleAuthCallback();
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
      <div className="text-center">
        {/* Animated Koi Logo */}
        <div className="w-16 h-16 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-6 animate-koi-bob border-2 border-white/20">
          {/* Koi Fish SVG */}
          <svg width="40" height="40" viewBox="0 0 100 100" className="drop-shadow-lg">
            {/* Koi body - cyan to purple gradient */}
            <ellipse cx="40" cy="50" rx="25" ry="20" fill="url(#koiGradientCallback)" />

            {/* Tail fin */}
            <path d="M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50" fill="url(#finGradientCallback)" />

            {/* Top fin */}
            <path d="M35 30 Q30 20 40 25 Q45 30 35 30" fill="url(#finGradientCallback)" />

            {/* Side fins */}
            <ellipse cx="30" cy="60" rx="8" ry="4" fill="url(#finGradientCallback)" transform="rotate(30 30 60)" />
            <ellipse cx="50" cy="60" rx="8" ry="4" fill="url(#finGradientCallback)" transform="rotate(-30 50 60)" />

            {/* Eyes */}
            <circle cx="45" cy="45" r="4" fill="white" />
            <circle cx="45" cy="45" r="2.5" fill="black" />
            <circle cx="46" cy="44" r="1" fill="white" />

            {/* Spots */}
            <circle cx="35" cy="40" r="2" fill="#90EE90" opacity="0.8" />
            <circle cx="40" cy="35" r="1.5" fill="#90EE90" opacity="0.8" />
            <circle cx="30" cy="50" r="1.5" fill="#90EE90" opacity="0.8" />

            {/* Mouth */}
            <path d="M55 50 Q60 52 55 54" stroke="black" strokeWidth="1" fill="none" />

            {/* Gradients */}
            <defs>
              <linearGradient id="koiGradientCallback" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#00FFFF" />
                <stop offset="50%" stopColor="#0080FF" />
                <stop offset="100%" stopColor="#8000FF" />
              </linearGradient>
              <linearGradient id="finGradientCallback" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#FF69B4" />
                <stop offset="50%" stopColor="#DA70D6" />
                <stop offset="100%" stopColor="#9370DB" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-4">Authenticating...</h2>
        <p className="text-blue-200">Please wait while we sign you in.</p>
        
        {/* Loading spinner */}
        <div className="mt-6">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    </div>
  );
}
