# 🐠 Koi Joyful Habits Hub

A comprehensive productivity and wellness app that gamifies your daily routine with AI assistance, built with Next.js, TypeScript, and Tailwind CSS.

## ✨ Features

### 📊 Productivity Activities
- **Notes**: Create, organize, and manage notes with templates and AI assistance
- **Tasks**: Task management with recurring tasks, folders, and categories
- **Content Creator**: Content planning and creation tools
- **Calendar**: Multi-source calendar integration
- **Email**: Email management and organization
- **Contacts**: Contact management with integration across features
- **Daily Diary**: Personal journaling with multimedia support
- **Healthy Living**: Health and fitness tracking
- **Favorite Websites**: Bookmark management

### 🎉 Fun Activities
- **Fun Facts & Quotes**: Daily inspiration and entertainment
- **Excuse Generator**: Creative excuse generation for any situation
- **Companions**: Virtual pets and mascots that grow with you
- **My Achievements**: Badge and trophy system
- **Recipes**: Recipe management with AI menu planning
- **My Pictures**: Photo organization and sharing
- **KOI Adventure Stories**: Ongoing storylines and adventures
- **Mini Games**: Relaxing games with rewards
- **Latest News**: Curated positive news

### 🎮 Gaming System
- **Level Up Game**: XP system for all activities
- **Daily Check-ins**: Streak tracking and rewards
- **Quests**: Daily, weekly, and monthly challenges
- **Mystery Boxes**: Random rewards and surprises
- **Visualizations**: Multiple scenic environments that evolve

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (for backend)

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd koi-app3
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🗄️ Database Setup (Supabase)

### Create a new Supabase project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Copy your project URL and anon key to `.env.local`

### Database Schema
The app requires the following tables:
- `users` - User profiles and game data
- `notes` - Note storage with folders and tags
- `tasks` - Task management
- `achievements` - User achievements and badges
- `quests` - Daily/weekly/monthly challenges
- `companions` - Virtual pet data

## 🔧 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Forms**: React Hook Form + Zod

## 🎯 Roadmap

- [ ] Complete all productivity features
- [ ] Implement gaming system
- [ ] Add AI assistant integration
- [ ] Set up n8n automation workflows
- [ ] Mobile app development
- [ ] Advanced analytics and insights

## 🤝 Contributing

This is a personal project, but suggestions and feedback are welcome!

## 📄 License

This project is private and proprietary.
