import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
}

export function formatDateTime(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function calculateXP(action: string): number {
  const xpValues: Record<string, number> = {
    'daily_checkin': 50,
    'create_note': 25,
    'create_task': 25,
    'complete_task': 50,
    'share_content': 30,
    'use_template': 20,
    'add_tag': 10,
    'organize_folder': 15,
    'mystery_box': 100,
    'quest_complete': 100,
  };
  
  return xpValues[action] || 10;
}

export function getLevelFromXP(xp: number): number {
  // Simple level calculation: every 1000 XP = 1 level
  return Math.floor(xp / 1000) + 1;
}

export function getXPForNextLevel(currentXP: number): number {
  const currentLevel = getLevelFromXP(currentXP);
  return currentLevel * 1000 - currentXP;
}
