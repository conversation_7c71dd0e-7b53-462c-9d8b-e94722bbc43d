'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Folder, Tag, Calendar, Trash2, Edit3, Bot, X, Save, ArrowLeft, CheckSquare, Clock, AlertCircle, Star } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import AIAssistant from '../ui/AIAssistant';
import { useAuth } from '../../contexts/AuthContext';
import { tasksService, type Task } from '../../lib/database';
import { awardXP, completeTask } from '../../lib/supabase';

interface TasksProps {
  onBack?: () => void;
}

const Tasks = ({ onBack }: TasksProps) => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [showCompleted, setShowCompleted] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const folders = ['Personal', 'Work', 'Projects', 'Health'];
  const priorities = ['low', 'medium', 'high'] as const;

  // Load tasks on component mount
  useEffect(() => {
    if (user) {
      loadTasks();
    }
  }, [user]);

  const loadTasks = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await tasksService.getAll(user.id);
      if (error) {
        setError(error.message);
      } else {
        setTasks(data || []);
      }
    } catch (err) {
      setError('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTask = async (taskData?: Partial<Task>) => {
    if (!user) return;

    const newTask = {
      user_id: user.id,
      title: taskData?.title || 'New Task',
      description: taskData?.description || '',
      completed: false,
      folder: taskData?.folder || 'Personal',
      tags: taskData?.tags || [],
      priority: taskData?.priority || 'medium',
      due_date: taskData?.due_date,
      recurring: taskData?.recurring
    };

    try {
      const { data, error } = await tasksService.create(newTask);
      if (error) {
        setError(error.message);
      } else if (data) {
        setTasks(prev => [data, ...prev]);
        setIsCreating(false);
        setEditingTask(data);

        // Award XP for creating a task
        await awardXP(user.id, 25, 'Created a task', 'productivity', data.id, 'task');
      }
    } catch (err) {
      setError('Failed to create task');
    }
  };

  const handleUpdateTask = async (taskId: string, updates: Partial<Task>) => {
    try {
      const { data, error } = await tasksService.update(taskId, updates);
      if (error) {
        setError(error.message);
      } else if (data) {
        setTasks(prev => prev.map(task => task.id === taskId ? data : task));
        setEditingTask(null);
      }
    } catch (err) {
      setError('Failed to update task');
    }
  };

  const handleToggleComplete = async (task: Task) => {
    if (!task.completed) {
      // Use the complete_task function which awards XP automatically
      try {
        const { data, error } = await completeTask(task.id);
        if (error) {
          setError(error.message);
        } else {
          // Update local state
          setTasks(prev => prev.map(t =>
            t.id === task.id
              ? { ...t, completed: true, completed_at: new Date().toISOString() }
              : t
          ));

          // Show XP reward notification
          if (data?.xp_earned) {
            alert(`Task completed! You earned ${data.xp_earned} XP!`);
          }
        }
      } catch (err) {
        setError('Failed to complete task');
      }
    } else {
      // Uncomplete task
      const updates = {
        completed: false,
        completed_at: null
      };
      await handleUpdateTask(task.id, updates);
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      const { error } = await tasksService.delete(taskId, false); // soft delete
      if (error) {
        setError(error.message);
      } else {
        setTasks(prev => prev.filter(task => task.id !== taskId));
      }
    } catch (err) {
      setError('Failed to delete task');
    }
  };

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFolder = selectedFolder === 'all' || task.folder.toLowerCase() === selectedFolder;
    const matchesPriority = selectedPriority === 'all' || task.priority === selectedPriority;
    const matchesCompleted = showCompleted || !task.completed;
    return matchesSearch && matchesFolder && matchesPriority && matchesCompleted;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-blue-400';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertCircle;
      case 'medium': return Clock;
      case 'low': return Star;
      default: return Clock;
    }
  };

  // Show task editor if editing
  if (editingTask) {
    return <TaskEditor 
      task={editingTask} 
      onSave={handleUpdateTask} 
      onCancel={() => setEditingTask(null)} 
    />;
  }

  return (
    <div className="space-y-6">
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
          <button 
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button 
              onClick={onBack}
              variant="outline" 
              className="border-blue-400/50 text-blue-200 hover:bg-blue-500/30"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-white">Tasks</h1>
            <p className="text-blue-200">Manage your tasks with priorities, due dates, and gamification</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={() => handleCreateTask()}>
            <Plus className="w-4 h-4 mr-2" />
            New Task
          </Button>
          <AIAssistant
            context="tasks"
            placeholder="Ask me to help with task management, productivity tips..."
          />
        </div>
      </div>

      {/* Filters */}
      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-300 w-4 h-4" />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedFolder}
              onChange={(e) => setSelectedFolder(e.target.value)}
              className="px-3 py-2 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
            >
              <option value="all" className="bg-blue-800">All Folders</option>
              {folders.map(folder => (
                <option key={folder} value={folder.toLowerCase()} className="bg-blue-800">{folder}</option>
              ))}
            </select>
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="px-3 py-2 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
            >
              <option value="all" className="bg-blue-800">All Priorities</option>
              {priorities.map(priority => (
                <option key={priority} value={priority} className="bg-blue-800 capitalize">{priority}</option>
              ))}
            </select>
            <label className="flex items-center space-x-2 text-blue-200">
              <input
                type="checkbox"
                checked={showCompleted}
                onChange={(e) => setShowCompleted(e.target.checked)}
                className="rounded border-blue-400/50 bg-blue-600/30 text-blue-400 focus:ring-blue-400"
              />
              <span className="text-sm">Show Completed</span>
            </label>
          </div>
        </div>
      </Card>

      {loading ? (
        <Card className="text-center py-12">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-blue-200">Loading your tasks...</p>
        </Card>
      ) : filteredTasks.length === 0 ? (
        <Card className="text-center py-12 bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-blue-500/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckSquare className="w-8 h-8 text-blue-300" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {tasks.length === 0 ? 'No tasks yet' : 'No matching tasks'}
            </h3>
            <p className="text-blue-200 mb-6">
              {tasks.length === 0 
                ? 'Create your first task to start being productive!'
                : 'Try adjusting your search or filters.'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={() => handleCreateTask()}>
                <Plus className="w-4 h-4 mr-2" />
                {tasks.length === 0 ? 'Create First Task' : 'Create New Task'}
              </Button>
              <AIAssistant
                context="tasks-empty"
                placeholder="Ask me for productivity tips or task templates..."
              />
            </div>
          </div>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredTasks.map(task => {
            const PriorityIcon = getPriorityIcon(task.priority);
            return (
              <Card key={task.id} className={`hover:shadow-lg transition-all bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30 ${task.completed ? 'opacity-60' : ''}`}>
                <div className="flex items-start space-x-4">
                  {/* Checkbox */}
                  <button
                    onClick={() => handleToggleComplete(task)}
                    className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                      task.completed 
                        ? 'bg-green-500 border-green-500 text-white' 
                        : 'border-blue-400/50 hover:border-blue-400'
                    }`}
                  >
                    {task.completed && <CheckSquare className="w-3 h-3" />}
                  </button>

                  {/* Task Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 
                        className={`font-semibold text-white cursor-pointer hover:text-blue-200 ${task.completed ? 'line-through' : ''}`}
                        onClick={() => setEditingTask(task)}
                      >
                        {task.title}
                      </h3>
                      <div className="flex items-center space-x-1 ml-4">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => setEditingTask(task)}
                          className="text-blue-300 hover:text-white hover:bg-blue-500/30"
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteTask(task.id)}
                          className="text-red-300 hover:text-white hover:bg-red-500/30"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {task.description && (
                      <p className="text-blue-200 text-sm mb-3">{task.description}</p>
                    )}

                    <div className="flex flex-wrap items-center gap-4 text-xs">
                      {/* Priority */}
                      <div className={`flex items-center space-x-1 ${getPriorityColor(task.priority)}`}>
                        <PriorityIcon className="w-3 h-3" />
                        <span className="capitalize">{task.priority}</span>
                      </div>

                      {/* Folder */}
                      <div className="flex items-center space-x-1 text-blue-300">
                        <Folder className="w-3 h-3" />
                        <span>{task.folder}</span>
                      </div>

                      {/* Due Date */}
                      {task.due_date && (
                        <div className="flex items-center space-x-1 text-blue-300">
                          <Calendar className="w-3 h-3" />
                          <span>Due {new Date(task.due_date).toLocaleDateString()}</span>
                        </div>
                      )}

                      {/* Tags */}
                      {task.tags.map(tag => (
                        <span key={tag} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-400/30 text-blue-100">
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </span>
                      ))}

                      {/* Completion Date */}
                      {task.completed && task.completed_at && (
                        <div className="text-green-400 text-xs">
                          Completed {new Date(task.completed_at).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Task Editor Component
interface TaskEditorProps {
  task: Task;
  onSave: (taskId: string, updates: Partial<Task>) => void;
  onCancel: () => void;
}

const TaskEditor: React.FC<TaskEditorProps> = ({ task, onSave, onCancel }) => {
  const [title, setTitle] = useState(task.title);
  const [description, setDescription] = useState(task.description || '');
  const [folder, setFolder] = useState(task.folder);
  const [priority, setPriority] = useState(task.priority);
  const [tags, setTags] = useState(task.tags.join(', '));
  const [dueDate, setDueDate] = useState(task.due_date ? task.due_date.split('T')[0] : '');
  const [recurring, setRecurring] = useState(task.recurring || '');

  const folders = ['Personal', 'Work', 'Projects', 'Health'];
  const priorities = ['low', 'medium', 'high'] as const;
  const recurringOptions = ['', 'daily', 'weekly', 'monthly'];

  const handleSave = () => {
    const updates: Partial<Task> = {
      title: title.trim() || 'Untitled Task',
      description: description.trim(),
      folder,
      priority,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      due_date: dueDate || undefined,
      recurring: recurring || undefined
    };

    onSave(task.id, updates);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">Edit Task</h1>
        <div className="flex items-center space-x-3">
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button onClick={onCancel} variant="outline" className="border-red-400/50 text-red-300 hover:bg-red-500/30">
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">Title</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              placeholder="Enter task title..."
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent resize-none"
              placeholder="Enter task description..."
            />
          </div>

          {/* Metadata Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Folder</label>
              <select
                value={folder}
                onChange={(e) => setFolder(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              >
                {folders.map(f => (
                  <option key={f} value={f} className="bg-blue-800">{f}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Priority</label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              >
                {priorities.map(p => (
                  <option key={p} value={p} className="bg-blue-800 capitalize">{p}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Due Date</label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Recurring</label>
              <select
                value={recurring}
                onChange={(e) => setRecurring(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              >
                <option value="" className="bg-blue-800">None</option>
                {recurringOptions.slice(1).map(option => (
                  <option key={option} value={option} className="bg-blue-800 capitalize">{option}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">Tags (comma-separated)</label>
            <input
              type="text"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              placeholder="tag1, tag2, tag3"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Tasks;
