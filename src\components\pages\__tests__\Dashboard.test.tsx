import { render, screen, fireEvent } from '@testing-library/react'
import { AuthProvider } from '../../../contexts/AuthContext'
import Dashboard from '../Dashboard'

// Mock the AuthContext
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {},
}

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: mockUser,
    loading: false,
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    signInWithGoogle: jest.fn(),
  }

  return (
    <AuthProvider value={mockAuthValue as any}>
      {children}
    </AuthProvider>
  )
}

describe('Dashboard Component', () => {
  const mockOnNavigate = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders dashboard with all sections', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // Check for main sections
    expect(screen.getByText('Productivity Tools')).toBeInTheDocument()
    expect(screen.getByText('Fun Activities')).toBeInTheDocument()
    expect(screen.getByText('Gaming Elements')).toBeInTheDocument()
  })

  it('displays user progress card', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    expect(screen.getByText(/Level \d+/)).toBeInTheDocument()
    expect(screen.getByText(/XP Total/)).toBeInTheDocument()
  })

  it('renders productivity features', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // Check for key productivity features
    expect(screen.getByText('Notes')).toBeInTheDocument()
    expect(screen.getByText('Tasks')).toBeInTheDocument()
    expect(screen.getByText('Calendar')).toBeInTheDocument()
  })

  it('renders fun activities', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // Check for fun features
    expect(screen.getByText('Daily Facts')).toBeInTheDocument()
    expect(screen.getByText('Excuse Generator')).toBeInTheDocument()
    expect(screen.getByText('Recipes')).toBeInTheDocument()
  })

  it('renders gaming elements', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // Check for gaming features
    expect(screen.getByText('Level Up')).toBeInTheDocument()
    expect(screen.getByText('Daily Quests')).toBeInTheDocument()
    expect(screen.getByText('Achievements')).toBeInTheDocument()
  })

  it('calls onNavigate when productivity feature is clicked', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    const notesButton = screen.getByText('Notes').closest('button')
    expect(notesButton).toBeInTheDocument()
    
    fireEvent.click(notesButton!)
    expect(mockOnNavigate).toHaveBeenCalledWith('notes')
  })

  it('calls onNavigate when gaming feature is clicked', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    const levelButton = screen.getByText('Level Up').closest('button')
    expect(levelButton).toBeInTheDocument()
    
    fireEvent.click(levelButton!)
    expect(mockOnNavigate).toHaveBeenCalledWith('level')
  })

  it('displays progress bar with correct styling', () => {
    render(
      <MockAuthProvider>
        <Dashboard onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // Check for progress bar elements
    const progressCard = screen.getByText(/Level \d+/).closest('div')
    expect(progressCard).toHaveClass('cursor-pointer')
  })

  it('supports different languages', () => {
    render(
      <MockAuthProvider>
        <Dashboard language="es" onNavigate={mockOnNavigate} />
      </MockAuthProvider>
    )

    // The component should render without errors with different language
    expect(screen.getByText('Productivity Tools')).toBeInTheDocument()
  })

  it('handles missing onNavigate prop gracefully', () => {
    render(
      <MockAuthProvider>
        <Dashboard />
      </MockAuthProvider>
    )

    const notesButton = screen.getByText('Notes').closest('button')
    expect(notesButton).toBeInTheDocument()
    
    // Should not throw error when clicked without onNavigate
    fireEvent.click(notesButton!)
  })
})
