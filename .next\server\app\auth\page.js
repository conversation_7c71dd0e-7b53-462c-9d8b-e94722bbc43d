/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/page";
exports.ids = ["app/auth/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(rsc)/./src/app/auth/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/page\",\n        pathname: \"/auth\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(rsc)/./src/app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NoaWNrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2tvaS1hcHAzJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGNoaWNrXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGtvaS1hcHAzXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2hpY2tcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xca29pLWFwcDNcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\koi-app3\\src\\app\\auth\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7fa9e9c8fafb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNoaWNrXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGtvaS1hcHAzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZmE5ZTljOGZhZmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Koi Joyful Habits Hub\",\n    description: \"Your Ultimate AI Productivity Assistant - Transform your daily routine into a joyful journey of productivity, fun, and personal growth.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\koi-app3\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\koi-app3\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/page.tsx */ \"(ssr)/./src/app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NoaWNrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2tvaS1hcHAzJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGNoaWNrXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGtvaS1hcHAzXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cchick%5C%5CDocuments%5C%5Caugment-projects%5C%5Ckoi-app3%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_pages_Auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/pages/Auth */ \"(ssr)/./src/components/pages/Auth.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AuthPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentLanguage, setCurrentLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    // Load saved language preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthPage.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('koi-app-language');\n            if (savedLanguage) {\n                setCurrentLanguage(savedLanguage);\n            }\n        }\n    }[\"AuthPage.useEffect\"], []);\n    // Save language preference\n    const handleLanguageChange = (language)=>{\n        setCurrentLanguage(language);\n        localStorage.setItem('koi-app-language', language);\n    };\n    // Redirect to dashboard if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthPage.useEffect\": ()=>{\n            if (!loading && user) {\n                router.push('/');\n            }\n        }\n    }[\"AuthPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Show loading while checking auth state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-6 animate-bounce border-2 border-white/20\",\n                        style: {\n                            animationDuration: '3s'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"40\",\n                            height: \"40\",\n                            viewBox: \"0 0 100 100\",\n                            className: \"drop-shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"40\",\n                                    cy: \"50\",\n                                    rx: \"25\",\n                                    ry: \"20\",\n                                    fill: \"url(#koiGradientAuthPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50\",\n                                    fill: \"url(#finGradientAuthPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M35 30 Q30 20 40 25 Q45 30 35 30\",\n                                    fill: \"url(#finGradientAuthPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"30\",\n                                    cy: \"60\",\n                                    rx: \"8\",\n                                    ry: \"4\",\n                                    fill: \"url(#finGradientAuthPage)\",\n                                    transform: \"rotate(30 30 60)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"50\",\n                                    cy: \"60\",\n                                    rx: \"8\",\n                                    ry: \"4\",\n                                    fill: \"url(#finGradientAuthPage)\",\n                                    transform: \"rotate(-30 50 60)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"45\",\n                                    cy: \"45\",\n                                    r: \"4\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"45\",\n                                    cy: \"45\",\n                                    r: \"2.5\",\n                                    fill: \"black\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"46\",\n                                    cy: \"44\",\n                                    r: \"1\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"35\",\n                                    cy: \"40\",\n                                    r: \"2\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"40\",\n                                    cy: \"35\",\n                                    r: \"1.5\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"30\",\n                                    cy: \"50\",\n                                    r: \"1.5\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M55 50 Q60 52 55 54\",\n                                    stroke: \"black\",\n                                    strokeWidth: \"1\",\n                                    fill: \"none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                            id: \"koiGradientAuthPage\",\n                                            x1: \"0%\",\n                                            y1: \"0%\",\n                                            x2: \"100%\",\n                                            y2: \"100%\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"0%\",\n                                                    stopColor: \"#00FFFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"50%\",\n                                                    stopColor: \"#0080FF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"100%\",\n                                                    stopColor: \"#8000FF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                            id: \"finGradientAuthPage\",\n                                            x1: \"0%\",\n                                            y1: \"0%\",\n                                            x2: \"100%\",\n                                            y2: \"100%\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"0%\",\n                                                    stopColor: \"#FF69B4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"50%\",\n                                                    stopColor: \"#DA70D6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"100%\",\n                                                    stopColor: \"#9370DB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render auth page if user is authenticated\n    if (user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_Auth__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        language: currentLanguage,\n        onLanguageChange: handleLanguageChange\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\auth\\\\page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2lCO0FBQ1I7QUFDTjtBQUVoQyxTQUFTTTtJQUN0QixNQUFNQyxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTU0sZUFBZUwsZ0VBQWVBO0lBQ3BDLE1BQU0sRUFBRU0sSUFBSSxFQUFFQyxPQUFPLEVBQUUsR0FBR04sOERBQU9BO0lBQ2pDLE1BQU0sQ0FBQ08saUJBQWlCQyxtQkFBbUIsR0FBR1osK0NBQVFBLENBQUM7SUFFdkQsaUNBQWlDO0lBQ2pDQyxnREFBU0E7OEJBQUM7WUFDUixNQUFNWSxnQkFBZ0JDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQyxJQUFJRixlQUFlO2dCQUNqQkQsbUJBQW1CQztZQUNyQjtRQUNGOzZCQUFHLEVBQUU7SUFFTCwyQkFBMkI7SUFDM0IsTUFBTUcsdUJBQXVCLENBQUNDO1FBQzVCTCxtQkFBbUJLO1FBQ25CSCxhQUFhSSxPQUFPLENBQUMsb0JBQW9CRDtJQUMzQztJQUVBLGlEQUFpRDtJQUNqRGhCLGdEQUFTQTs4QkFBQztZQUNSLElBQUksQ0FBQ1MsV0FBV0QsTUFBTTtnQkFDcEJGLE9BQU9ZLElBQUksQ0FBQztZQUNkO1FBQ0Y7NkJBQUc7UUFBQ1Y7UUFBTUM7UUFBU0g7S0FBTztJQUUxQix5Q0FBeUM7SUFDekMsSUFBSUcsU0FBUztRQUNYLHFCQUNFLDhEQUFDVTtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO3dCQUFxTEMsT0FBTzs0QkFBRUMsbUJBQW1CO3dCQUFLO2tDQUVuTyw0RUFBQ0M7NEJBQUlDLE9BQU07NEJBQUtDLFFBQU87NEJBQUtDLFNBQVE7NEJBQWNOLFdBQVU7OzhDQUUxRCw4REFBQ087b0NBQVFDLElBQUc7b0NBQUtDLElBQUc7b0NBQUtDLElBQUc7b0NBQUtDLElBQUc7b0NBQUtDLE1BQUs7Ozs7Ozs4Q0FHOUMsOERBQUNDO29DQUFLQyxHQUFFO29DQUFxRkYsTUFBSzs7Ozs7OzhDQUdsRyw4REFBQ0M7b0NBQUtDLEdBQUU7b0NBQW1DRixNQUFLOzs7Ozs7OENBR2hELDhEQUFDTDtvQ0FBUUMsSUFBRztvQ0FBS0MsSUFBRztvQ0FBS0MsSUFBRztvQ0FBSUMsSUFBRztvQ0FBSUMsTUFBSztvQ0FBNEJHLFdBQVU7Ozs7Ozs4Q0FDbEYsOERBQUNSO29DQUFRQyxJQUFHO29DQUFLQyxJQUFHO29DQUFLQyxJQUFHO29DQUFJQyxJQUFHO29DQUFJQyxNQUFLO29DQUE0QkcsV0FBVTs7Ozs7OzhDQUdsRiw4REFBQ0M7b0NBQU9SLElBQUc7b0NBQUtDLElBQUc7b0NBQUtRLEdBQUU7b0NBQUlMLE1BQUs7Ozs7Ozs4Q0FDbkMsOERBQUNJO29DQUFPUixJQUFHO29DQUFLQyxJQUFHO29DQUFLUSxHQUFFO29DQUFNTCxNQUFLOzs7Ozs7OENBQ3JDLDhEQUFDSTtvQ0FBT1IsSUFBRztvQ0FBS0MsSUFBRztvQ0FBS1EsR0FBRTtvQ0FBSUwsTUFBSzs7Ozs7OzhDQUduQyw4REFBQ0k7b0NBQU9SLElBQUc7b0NBQUtDLElBQUc7b0NBQUtRLEdBQUU7b0NBQUlMLE1BQUs7b0NBQVVNLFNBQVE7Ozs7Ozs4Q0FDckQsOERBQUNGO29DQUFPUixJQUFHO29DQUFLQyxJQUFHO29DQUFLUSxHQUFFO29DQUFNTCxNQUFLO29DQUFVTSxTQUFROzs7Ozs7OENBQ3ZELDhEQUFDRjtvQ0FBT1IsSUFBRztvQ0FBS0MsSUFBRztvQ0FBS1EsR0FBRTtvQ0FBTUwsTUFBSztvQ0FBVU0sU0FBUTs7Ozs7OzhDQUd2RCw4REFBQ0w7b0NBQUtDLEdBQUU7b0NBQXNCSyxRQUFPO29DQUFRQyxhQUFZO29DQUFJUixNQUFLOzs7Ozs7OENBR2xFLDhEQUFDUzs7c0RBQ0MsOERBQUNDOzRDQUFlQyxJQUFHOzRDQUFzQkMsSUFBRzs0Q0FBS0MsSUFBRzs0Q0FBS0MsSUFBRzs0Q0FBT0MsSUFBRzs7OERBQ3BFLDhEQUFDQztvREFBS0MsUUFBTztvREFBS0MsV0FBVTs7Ozs7OzhEQUM1Qiw4REFBQ0Y7b0RBQUtDLFFBQU87b0RBQU1DLFdBQVU7Ozs7Ozs4REFDN0IsOERBQUNGO29EQUFLQyxRQUFPO29EQUFPQyxXQUFVOzs7Ozs7Ozs7Ozs7c0RBRWhDLDhEQUFDUjs0Q0FBZUMsSUFBRzs0Q0FBc0JDLElBQUc7NENBQUtDLElBQUc7NENBQUtDLElBQUc7NENBQU9DLElBQUc7OzhEQUNwRSw4REFBQ0M7b0RBQUtDLFFBQU87b0RBQUtDLFdBQVU7Ozs7Ozs4REFDNUIsOERBQUNGO29EQUFLQyxRQUFPO29EQUFNQyxXQUFVOzs7Ozs7OERBQzdCLDhEQUFDRjtvREFBS0MsUUFBTztvREFBT0MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXRDLDhEQUFDQzt3QkFBRy9CLFdBQVU7a0NBQXFDOzs7Ozs7a0NBQ25ELDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkI7SUFFQSxrREFBa0Q7SUFDbEQsSUFBSVosTUFBTTtRQUNSLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDSiw4REFBSUE7UUFDSFksVUFBVU47UUFDVjBDLGtCQUFrQnJDOzs7Ozs7QUFHeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2hpY2tcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xca29pLWFwcDNcXHNyY1xcYXBwXFxhdXRoXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi4vLi4vY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IEF1dGggZnJvbSAnLi4vLi4vY29tcG9uZW50cy9wYWdlcy9BdXRoJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aFBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbiAgY29uc3QgeyB1c2VyLCBsb2FkaW5nIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtjdXJyZW50TGFuZ3VhZ2UsIHNldEN1cnJlbnRMYW5ndWFnZV0gPSB1c2VTdGF0ZSgnZW4nKTtcblxuICAvLyBMb2FkIHNhdmVkIGxhbmd1YWdlIHByZWZlcmVuY2VcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzYXZlZExhbmd1YWdlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2tvaS1hcHAtbGFuZ3VhZ2UnKTtcbiAgICBpZiAoc2F2ZWRMYW5ndWFnZSkge1xuICAgICAgc2V0Q3VycmVudExhbmd1YWdlKHNhdmVkTGFuZ3VhZ2UpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIFNhdmUgbGFuZ3VhZ2UgcHJlZmVyZW5jZVxuICBjb25zdCBoYW5kbGVMYW5ndWFnZUNoYW5nZSA9IChsYW5ndWFnZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q3VycmVudExhbmd1YWdlKGxhbmd1YWdlKTtcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgna29pLWFwcC1sYW5ndWFnZScsIGxhbmd1YWdlKTtcbiAgfTtcblxuICAvLyBSZWRpcmVjdCB0byBkYXNoYm9hcmQgaWYgYWxyZWFkeSBhdXRoZW50aWNhdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFsb2FkaW5nICYmIHVzZXIpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvJyk7XG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgcm91dGVyXSk7XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHdoaWxlIGNoZWNraW5nIGF1dGggc3RhdGVcbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTYwMCB2aWEtYmx1ZS03MDAgdG8tYmx1ZS04MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIHsvKiBBbmltYXRlZCBLb2kgTG9nbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1iciBmcm9tLWN5YW4tMzAwIHZpYS1ibHVlLTQwMCB0by1wdXJwbGUtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctMnhsIG14LWF1dG8gbWItNiBhbmltYXRlLWJvdW5jZSBib3JkZXItMiBib3JkZXItd2hpdGUvMjBcIiBzdHlsZT17eyBhbmltYXRpb25EdXJhdGlvbjogJzNzJyB9fT5cbiAgICAgICAgICAgIHsvKiBLb2kgRmlzaCBTVkcgKi99XG4gICAgICAgICAgICA8c3ZnIHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIHZpZXdCb3g9XCIwIDAgMTAwIDEwMFwiIGNsYXNzTmFtZT1cImRyb3Atc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgIHsvKiBLb2kgYm9keSAtIGN5YW4gdG8gcHVycGxlIGdyYWRpZW50ICovfVxuICAgICAgICAgICAgICA8ZWxsaXBzZSBjeD1cIjQwXCIgY3k9XCI1MFwiIHJ4PVwiMjVcIiByeT1cIjIwXCIgZmlsbD1cInVybCgja29pR3JhZGllbnRBdXRoUGFnZSlcIiAvPlxuXG4gICAgICAgICAgICAgIHsvKiBUYWlsIGZpbiAqL31cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk0xNSA1MCBRNSA0MCAxMCAzMCBRMTUgMzUgMjAgNDAgUTE1IDQ1IDEwIDUwIFExNSA1NSAyMCA2MCBRMTUgNjUgMTAgNzAgUTUgNjAgMTUgNTBcIiBmaWxsPVwidXJsKCNmaW5HcmFkaWVudEF1dGhQYWdlKVwiIC8+XG5cbiAgICAgICAgICAgICAgey8qIFRvcCBmaW4gKi99XG4gICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMzUgMzAgUTMwIDIwIDQwIDI1IFE0NSAzMCAzNSAzMFwiIGZpbGw9XCJ1cmwoI2ZpbkdyYWRpZW50QXV0aFBhZ2UpXCIgLz5cblxuICAgICAgICAgICAgICB7LyogU2lkZSBmaW5zICovfVxuICAgICAgICAgICAgICA8ZWxsaXBzZSBjeD1cIjMwXCIgY3k9XCI2MFwiIHJ4PVwiOFwiIHJ5PVwiNFwiIGZpbGw9XCJ1cmwoI2ZpbkdyYWRpZW50QXV0aFBhZ2UpXCIgdHJhbnNmb3JtPVwicm90YXRlKDMwIDMwIDYwKVwiIC8+XG4gICAgICAgICAgICAgIDxlbGxpcHNlIGN4PVwiNTBcIiBjeT1cIjYwXCIgcng9XCI4XCIgcnk9XCI0XCIgZmlsbD1cInVybCgjZmluR3JhZGllbnRBdXRoUGFnZSlcIiB0cmFuc2Zvcm09XCJyb3RhdGUoLTMwIDUwIDYwKVwiIC8+XG5cbiAgICAgICAgICAgICAgey8qIEV5ZXMgKi99XG4gICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCI0NVwiIGN5PVwiNDVcIiByPVwiNFwiIGZpbGw9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCI0NVwiIGN5PVwiNDVcIiByPVwiMi41XCIgZmlsbD1cImJsYWNrXCIgLz5cbiAgICAgICAgICAgICAgPGNpcmNsZSBjeD1cIjQ2XCIgY3k9XCI0NFwiIHI9XCIxXCIgZmlsbD1cIndoaXRlXCIgLz5cblxuICAgICAgICAgICAgICB7LyogU3BvdHMgKi99XG4gICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCIzNVwiIGN5PVwiNDBcIiByPVwiMlwiIGZpbGw9XCIjOTBFRTkwXCIgb3BhY2l0eT1cIjAuOFwiIC8+XG4gICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCI0MFwiIGN5PVwiMzVcIiByPVwiMS41XCIgZmlsbD1cIiM5MEVFOTBcIiBvcGFjaXR5PVwiMC44XCIgLz5cbiAgICAgICAgICAgICAgPGNpcmNsZSBjeD1cIjMwXCIgY3k9XCI1MFwiIHI9XCIxLjVcIiBmaWxsPVwiIzkwRUU5MFwiIG9wYWNpdHk9XCIwLjhcIiAvPlxuXG4gICAgICAgICAgICAgIHsvKiBNb3V0aCAqL31cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk01NSA1MCBRNjAgNTIgNTUgNTRcIiBzdHJva2U9XCJibGFja1wiIHN0cm9rZVdpZHRoPVwiMVwiIGZpbGw9XCJub25lXCIgLz5cblxuICAgICAgICAgICAgICB7LyogR3JhZGllbnRzICovfVxuICAgICAgICAgICAgICA8ZGVmcz5cbiAgICAgICAgICAgICAgICA8bGluZWFyR3JhZGllbnQgaWQ9XCJrb2lHcmFkaWVudEF1dGhQYWdlXCIgeDE9XCIwJVwiIHkxPVwiMCVcIiB4Mj1cIjEwMCVcIiB5Mj1cIjEwMCVcIj5cbiAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzAwRkZGRlwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCI1MCVcIiBzdG9wQ29sb3I9XCIjMDA4MEZGXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjEwMCVcIiBzdG9wQ29sb3I9XCIjODAwMEZGXCIgLz5cbiAgICAgICAgICAgICAgICA8L2xpbmVhckdyYWRpZW50PlxuICAgICAgICAgICAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD1cImZpbkdyYWRpZW50QXV0aFBhZ2VcIiB4MT1cIjAlXCIgeTE9XCIwJVwiIHgyPVwiMTAwJVwiIHkyPVwiMTAwJVwiPlxuICAgICAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiMCVcIiBzdG9wQ29sb3I9XCIjRkY2OUI0XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjUwJVwiIHN0b3BDb2xvcj1cIiNEQTcwRDZcIiAvPlxuICAgICAgICAgICAgICAgICAgPHN0b3Agb2Zmc2V0PVwiMTAwJVwiIHN0b3BDb2xvcj1cIiM5MzcwREJcIiAvPlxuICAgICAgICAgICAgICAgIDwvbGluZWFyR3JhZGllbnQ+XG4gICAgICAgICAgICAgIDwvZGVmcz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+TG9hZGluZy4uLjwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gRG9uJ3QgcmVuZGVyIGF1dGggcGFnZSBpZiB1c2VyIGlzIGF1dGhlbnRpY2F0ZWRcbiAgaWYgKHVzZXIpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEF1dGggXG4gICAgICBsYW5ndWFnZT17Y3VycmVudExhbmd1YWdlfVxuICAgICAgb25MYW5ndWFnZUNoYW5nZT17aGFuZGxlTGFuZ3VhZ2VDaGFuZ2V9XG4gICAgLz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZUF1dGgiLCJBdXRoIiwiQXV0aFBhZ2UiLCJyb3V0ZXIiLCJzZWFyY2hQYXJhbXMiLCJ1c2VyIiwibG9hZGluZyIsImN1cnJlbnRMYW5ndWFnZSIsInNldEN1cnJlbnRMYW5ndWFnZSIsInNhdmVkTGFuZ3VhZ2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiaGFuZGxlTGFuZ3VhZ2VDaGFuZ2UiLCJsYW5ndWFnZSIsInNldEl0ZW0iLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJhbmltYXRpb25EdXJhdGlvbiIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImVsbGlwc2UiLCJjeCIsImN5IiwicngiLCJyeSIsImZpbGwiLCJwYXRoIiwiZCIsInRyYW5zZm9ybSIsImNpcmNsZSIsInIiLCJvcGFjaXR5Iiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJkZWZzIiwibGluZWFyR3JhZGllbnQiLCJpZCIsIngxIiwieTEiLCJ4MiIsInkyIiwic3RvcCIsIm9mZnNldCIsInN0b3BDb2xvciIsImgyIiwib25MYW5ndWFnZUNoYW5nZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/pages/Auth.tsx":
/*!***************************************!*\
  !*** ./src/components/pages/Auth.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(ssr)/./src/lib/i18n/useTranslation.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Auth = ({ language = 'en', onLanguageChange })=>{\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(language);\n    const { signUp, signIn, signInWithMagicLink, signInWithGoogle } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showLanguageDropdown, setShowLanguageDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'en',\n            label: 'US English',\n            flag: '🇺🇸'\n        },\n        {\n            code: 'es',\n            label: 'ES Español',\n            flag: '🇪🇸'\n        },\n        {\n            code: 'fr',\n            label: 'FR Français',\n            flag: '🇫🇷'\n        },\n        {\n            code: 'de',\n            label: 'DE Deutsch',\n            flag: '🇩🇪'\n        },\n        {\n            code: 'it',\n            label: 'IT Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'pt',\n            label: 'PT Português',\n            flag: '🇵🇹'\n        },\n        {\n            code: 'nl',\n            label: 'NL Nederlands',\n            flag: '🇳🇱'\n        },\n        {\n            code: 'zh',\n            label: 'CN 中文',\n            flag: '🇨🇳'\n        },\n        {\n            code: 'ja',\n            label: 'JP 日本語',\n            flag: '🇯🇵'\n        },\n        {\n            code: 'ko',\n            label: 'KR 한국어',\n            flag: '🇰🇷'\n        },\n        {\n            code: 'ru',\n            label: 'RU Русский',\n            flag: '🇷🇺'\n        },\n        {\n            code: 'gr',\n            label: 'GR Ελληνικά',\n            flag: '🇬🇷'\n        }\n    ];\n    const currentLang = languages.find((lang)=>lang.code === language) || languages[0];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setMessage('');\n        try {\n            let result;\n            if (authMode === 'signup') {\n                result = await signUp(email, password);\n                if (!result.error) {\n                    setMessage('Check your email for verification link!');\n                }\n            } else if (authMode === 'signin') {\n                result = await signIn(email, password);\n            } else if (authMode === 'magic') {\n                result = await signInWithMagicLink(email);\n                if (!result.error) {\n                    setMessage('Check your email for the magic link!');\n                }\n            }\n            if (result?.error) {\n                setMessage(result.error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await signInWithGoogle();\n            if (error) {\n                setMessage(error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-3 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-gradient-to-br from-pink-200 via-purple-300 to-blue-300 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-2 animate-bounce border-4 border-white/30\",\n                                    style: {\n                                        animationDuration: '3s'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"48\",\n                                        height: \"48\",\n                                        viewBox: \"0 0 120 100\",\n                                        className: \"drop-shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"60\",\n                                                cy: \"50\",\n                                                rx: \"28\",\n                                                ry: \"22\",\n                                                fill: \"url(#koiBodyAuth)\",\n                                                stroke: \"url(#koiStrokeAuth)\",\n                                                strokeWidth: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M32 50 Q15 35 8 25 Q12 30 18 35 Q25 40 32 45 Q25 50 18 55 Q12 60 8 65 Q15 55 32 50\",\n                                                fill: \"url(#tailGradientAuth)\",\n                                                stroke: \"url(#koiStrokeAuth)\",\n                                                strokeWidth: \"1.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M50 28 Q45 18 55 22 Q65 26 60 32 Q55 30 50 28\",\n                                                fill: \"url(#finGradientAuth)\",\n                                                stroke: \"url(#koiStrokeAuth)\",\n                                                strokeWidth: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"45\",\n                                                cy: \"65\",\n                                                rx: \"10\",\n                                                ry: \"6\",\n                                                fill: \"url(#finGradientAuth)\",\n                                                stroke: \"url(#koiStrokeAuth)\",\n                                                strokeWidth: \"1\",\n                                                transform: \"rotate(25 45 65)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"75\",\n                                                cy: \"65\",\n                                                rx: \"10\",\n                                                ry: \"6\",\n                                                fill: \"url(#finGradientAuth)\",\n                                                stroke: \"url(#koiStrokeAuth)\",\n                                                strokeWidth: \"1\",\n                                                transform: \"rotate(-25 75 65)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"70\",\n                                                cy: \"42\",\n                                                r: \"6\",\n                                                fill: \"white\",\n                                                stroke: \"#333\",\n                                                strokeWidth: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"70\",\n                                                cy: \"42\",\n                                                r: \"4\",\n                                                fill: \"black\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"71.5\",\n                                                cy: \"40.5\",\n                                                r: \"1.5\",\n                                                fill: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M82 50 Q85 53 82 56\",\n                                                stroke: \"#333\",\n                                                strokeWidth: \"2\",\n                                                fill: \"none\",\n                                                strokeLinecap: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"50\",\n                                                cy: \"40\",\n                                                r: \"3\",\n                                                fill: \"#FFD700\",\n                                                opacity: \"0.9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"55\",\n                                                cy: \"35\",\n                                                r: \"2.5\",\n                                                fill: \"#90EE90\",\n                                                opacity: \"0.9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"45\",\n                                                cy: \"50\",\n                                                r: \"2\",\n                                                fill: \"#FFD700\",\n                                                opacity: \"0.9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"65\",\n                                                cy: \"55\",\n                                                r: \"2.5\",\n                                                fill: \"#90EE90\",\n                                                opacity: \"0.9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"58\",\n                                                cy: \"58\",\n                                                r: \"2\",\n                                                fill: \"#FFD700\",\n                                                opacity: \"0.9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"95\",\n                                                cy: \"30\",\n                                                r: \"2\",\n                                                fill: \"rgba(255,255,255,0.6)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"100\",\n                                                cy: \"40\",\n                                                r: \"1.5\",\n                                                fill: \"rgba(255,255,255,0.5)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"98\",\n                                                cy: \"25\",\n                                                r: \"1\",\n                                                fill: \"rgba(255,255,255,0.7)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                                                        id: \"koiBodyAuth\",\n                                                        cx: \"0.3\",\n                                                        cy: \"0.3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#00FFFF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"40%\",\n                                                                stopColor: \"#40E0D0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"70%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#FF69B4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"tailGradientAuth\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF69B4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"30%\",\n                                                                stopColor: \"#DA70D6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"70%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"finGradientAuth\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FFB6C1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#DDA0DD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"koiStrokeAuth\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF1493\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8A2BE2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-white mb-1\",\n                                children: \"Welcome to Koi App\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Your personal productivity assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 backdrop-blur-lg rounded-2xl p-4 shadow-2xl border border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLanguageDropdown(!showLanguageDropdown),\n                                        className: \"w-full flex items-center justify-center space-x-2 bg-blue-400/30 hover:bg-blue-400/50 px-3 py-2 rounded-lg transition-colors text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium\",\n                                                children: [\n                                                    currentLang.flag,\n                                                    \" \",\n                                                    currentLang.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full left-0 right-0 mt-2 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-60 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2\",\n                                            children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        onLanguageChange?.(lang.code);\n                                                        setShowLanguageDropdown(false);\n                                                    },\n                                                    className: `w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${language === lang.code ? 'bg-blue-400/50 text-white' : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: lang.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: lang.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, lang.code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-blue-600/50 rounded-xl p-1 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signin'),\n                                        className: `flex-1 py-2 px-2 rounded-lg text-xs font-semibold transition-all ${authMode === 'signin' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'}`,\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signup'),\n                                        className: `flex-1 py-2 px-2 rounded-lg text-xs font-semibold transition-all ${authMode === 'signup' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'}`,\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('magic'),\n                                        className: `flex-1 py-2 px-2 rounded-lg text-xs font-semibold transition-all ${authMode === 'magic' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'}`,\n                                        children: \"Magic Link\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-blue-200 text-xs font-medium mb-1\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-10 pr-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    authMode !== 'magic' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-blue-200 text-xs font-medium\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    authMode === 'signin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"text-blue-300 text-xs hover:text-white transition-colors\",\n                                                        children: \"Forgot?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: showPassword ? 'text' : 'password',\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-lg pl-10 pr-10 py-2.5 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30 text-sm\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 37\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 70\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-2.5 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: authMode === 'signin' ? 'Sign In' : authMode === 'signup' ? 'Sign Up' : 'Send Magic Link'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full border-t border-blue-400/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex justify-center text-xs\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 bg-blue-600/80 text-blue-200\",\n                                                    children: \"or\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGoogleSignIn,\n                                        disabled: loading,\n                                        className: \"w-full mt-2 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-2.5 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#4285F4\",\n                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#34A853\",\n                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#FBBC05\",\n                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#EA4335\",\n                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Continue with Google\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined),\n                            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `mt-2 p-2 rounded-lg text-xs ${message.includes('Check your email') ? 'bg-green-500/20 text-green-200 border border-green-400/30' : 'bg-red-500/20 text-red-200 border border-red-400/30'}`,\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setShowLanguageDropdown(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Auth);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/pages/Auth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { session } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.getSession();\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    setUser(session?.user ?? null);\n                    setLoading(false);\n                    // Handle different auth events\n                    if (event === 'SIGNED_IN') {\n                        console.log('User signed in:', session?.user?.email);\n                    } else if (event === 'SIGNED_OUT') {\n                        console.log('User signed out');\n                    } else if (event === 'TOKEN_REFRESHED') {\n                        console.log('Token refreshed');\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        session,\n        loading,\n        signUp: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.signUp,\n        signIn: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.signIn,\n        signInWithMagicLink: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.signInWithMagicLink,\n        signInWithGoogle: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.signInWithGoogle,\n        signOut: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.signOut,\n        resetPassword: _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.auth.resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n/translations.ts":
/*!**************************************!*\
  !*** ./src/lib/i18n/translations.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    en: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Your Ultimate AI Productivity Assistant',\n        aiAssistant: 'AI Assistant',\n        account: 'Account',\n        signOut: 'Sign Out',\n        // Dashboard\n        welcomeBack: 'Welcome back! 👋',\n        readyToday: 'Ready to make today amazing?',\n        yourProgress: 'Your Progress',\n        level: 'Level',\n        noviceExplorer: 'Novice Explorer',\n        xpToNextLevel: 'XP to next level',\n        howAreYouFeeling: 'How are you feeling?',\n        dailyInspiration: 'Daily Inspiration',\n        // Activities\n        productivityActivities: 'Productivity Activities',\n        productivityDescription: 'Tools to help you stay organized and efficient',\n        funActivities: 'Fun Activities',\n        funDescription: 'Take a break and have some fun!',\n        gamingActivities: 'Gaming Activities',\n        gamingDescription: 'Level up your life with gamification',\n        // Features\n        notes: 'Notes',\n        notesDesc: 'Capture and organize your thoughts',\n        tasks: 'Tasks',\n        tasksDesc: 'Manage your daily tasks and to-dos',\n        calendar: 'Calendar',\n        calendarDesc: 'Schedule and track your events',\n        email: 'Email',\n        emailDesc: 'Manage all your email accounts in one place',\n        contacts: 'Contacts',\n        contactsDesc: 'Keep track of important contacts',\n        diary: 'Daily Diary',\n        diaryDesc: 'Record your thoughts and feelings',\n        health: 'Healthy Living',\n        healthDesc: 'Track workouts and nutrition',\n        content: 'Content Creator',\n        contentDesc: 'Create blogs, reports, essays and social media posts',\n        websites: 'Favorite Websites',\n        websitesDesc: 'Organize and access your favorite websites',\n        // Gaming Features\n        levelUp: 'Level Up',\n        levelUpDesc: 'Track your progress and level up',\n        dailyQuests: 'Daily Quests',\n        dailyQuestsDesc: 'Complete challenges and earn rewards',\n        rewards: 'Rewards',\n        rewardsDesc: 'Unlock achievements and collect rewards',\n        leaderboard: 'Leaderboard',\n        leaderboardDesc: 'Compete with friends and climb ranks',\n        badges: 'Badges',\n        badgesDesc: 'Earn badges for your accomplishments',\n        streaks: 'Streaks',\n        streaksDesc: 'Maintain daily activity streaks',\n        // Settings\n        settings: 'Settings',\n        settingsDesc: 'Customize your Koi app experience',\n        profileSettings: 'Profile Settings',\n        profileDesc: 'Manage your account and personal information',\n        notifications: 'Notifications',\n        notificationsDesc: 'Configure alerts and reminders',\n        privacy: 'Privacy & Security',\n        privacyDesc: 'Control your data and security settings',\n        appearance: 'Appearance',\n        appearanceDesc: 'Customize themes and display options',\n        languageRegion: 'Language & Region',\n        languageDesc: 'Set your preferred language and locale',\n        dataManagement: 'Data Management',\n        dataDesc: 'Import, export, and backup your data',\n        helpSupport: 'Help & Support',\n        helpDesc: 'Get help and contact support',\n        aboutApp: 'About Koi App',\n        aboutDesc: 'Version info and legal information',\n        // AI Assistant\n        aiChatTitle: 'AI Assistant',\n        aiChatSubtitle: 'Always here to help',\n        typeMessage: 'Type your message...',\n        aiWelcome: 'Hello! I\\'m your AI assistant. How can I help you today?',\n        // Common\n        comingSoon: 'Coming Soon',\n        back: 'Back',\n        save: 'Save',\n        cancel: 'Cancel',\n        close: 'Close',\n        send: 'Send'\n    },\n    es: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Tu Asistente de Productividad AI Definitivo',\n        aiAssistant: 'Asistente IA',\n        account: 'Cuenta',\n        signOut: 'Cerrar Sesión',\n        // Dashboard\n        welcomeBack: '¡Bienvenido de vuelta! 👋',\n        readyToday: '¿Listo para hacer hoy increíble?',\n        yourProgress: 'Tu Progreso',\n        level: 'Nivel',\n        noviceExplorer: 'Explorador Novato',\n        xpToNextLevel: 'XP al siguiente nivel',\n        howAreYouFeeling: '¿Cómo te sientes?',\n        dailyInspiration: 'Inspiración Diaria',\n        // Activities\n        productivityActivities: 'Actividades de Productividad',\n        productivityDescription: 'Herramientas para mantenerte organizado y eficiente',\n        funActivities: 'Actividades Divertidas',\n        funDescription: '¡Tómate un descanso y diviértete!',\n        gamingActivities: 'Actividades de Juego',\n        gamingDescription: 'Sube de nivel tu vida con gamificación',\n        // Features\n        notes: 'Notas',\n        notesDesc: 'Captura y organiza tus pensamientos',\n        tasks: 'Tareas',\n        tasksDesc: 'Gestiona tus tareas diarias y pendientes',\n        calendar: 'Calendario',\n        calendarDesc: 'Programa y rastrea tus eventos',\n        email: 'Correo',\n        emailDesc: 'Gestiona todas tus cuentas de correo en un lugar',\n        contacts: 'Contactos',\n        contactsDesc: 'Mantén registro de contactos importantes',\n        diary: 'Diario Personal',\n        diaryDesc: 'Registra tus pensamientos y sentimientos',\n        health: 'Vida Saludable',\n        healthDesc: 'Rastrea ejercicios y nutrición',\n        content: 'Creador de Contenido',\n        contentDesc: 'Crea blogs, informes, ensayos y publicaciones sociales',\n        websites: 'Sitios Web Favoritos',\n        websitesDesc: 'Organiza y accede a tus sitios web favoritos',\n        // Gaming Features\n        levelUp: 'Subir Nivel',\n        levelUpDesc: 'Rastrea tu progreso y sube de nivel',\n        dailyQuests: 'Misiones Diarias',\n        dailyQuestsDesc: 'Completa desafíos y gana recompensas',\n        rewards: 'Recompensas',\n        rewardsDesc: 'Desbloquea logros y colecciona recompensas',\n        leaderboard: 'Tabla de Líderes',\n        leaderboardDesc: 'Compite con amigos y escala posiciones',\n        badges: 'Insignias',\n        badgesDesc: 'Gana insignias por tus logros',\n        streaks: 'Rachas',\n        streaksDesc: 'Mantén rachas de actividad diaria',\n        // Settings\n        settings: 'Configuración',\n        settingsDesc: 'Personaliza tu experiencia con Koi app',\n        profileSettings: 'Configuración de Perfil',\n        profileDesc: 'Gestiona tu cuenta e información personal',\n        notifications: 'Notificaciones',\n        notificationsDesc: 'Configura alertas y recordatorios',\n        privacy: 'Privacidad y Seguridad',\n        privacyDesc: 'Controla tus datos y configuración de seguridad',\n        appearance: 'Apariencia',\n        appearanceDesc: 'Personaliza temas y opciones de visualización',\n        languageRegion: 'Idioma y Región',\n        languageDesc: 'Establece tu idioma y configuración regional preferidos',\n        dataManagement: 'Gestión de Datos',\n        dataDesc: 'Importa, exporta y respalda tus datos',\n        helpSupport: 'Ayuda y Soporte',\n        helpDesc: 'Obtén ayuda y contacta soporte',\n        aboutApp: 'Acerca de Koi App',\n        aboutDesc: 'Información de versión e información legal',\n        // AI Assistant\n        aiChatTitle: 'Asistente IA',\n        aiChatSubtitle: 'Siempre aquí para ayudar',\n        typeMessage: 'Escribe tu mensaje...',\n        aiWelcome: '¡Hola! Soy tu asistente IA. ¿Cómo puedo ayudarte hoy?',\n        // Common\n        comingSoon: 'Próximamente',\n        back: 'Atrás',\n        save: 'Guardar',\n        cancel: 'Cancelar',\n        close: 'Cerrar',\n        send: 'Enviar'\n    },\n    fr: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Votre Assistant de Productivité IA Ultime',\n        aiAssistant: 'Assistant IA',\n        account: 'Compte',\n        signOut: 'Se Déconnecter',\n        // Dashboard\n        welcomeBack: 'Bon retour ! 👋',\n        readyToday: 'Prêt à rendre aujourd\\'hui incroyable ?',\n        yourProgress: 'Votre Progrès',\n        level: 'Niveau',\n        noviceExplorer: 'Explorateur Novice',\n        xpToNextLevel: 'XP au niveau suivant',\n        howAreYouFeeling: 'Comment vous sentez-vous ?',\n        dailyInspiration: 'Inspiration Quotidienne',\n        // Activities\n        productivityActivities: 'Activités de Productivité',\n        productivityDescription: 'Outils pour vous aider à rester organisé et efficace',\n        funActivities: 'Activités Amusantes',\n        funDescription: 'Prenez une pause et amusez-vous !',\n        gamingActivities: 'Activités de Jeu',\n        gamingDescription: 'Améliorez votre vie avec la gamification',\n        // Common\n        comingSoon: 'Bientôt Disponible',\n        back: 'Retour',\n        save: 'Sauvegarder',\n        cancel: 'Annuler',\n        close: 'Fermer',\n        send: 'Envoyer'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n/translations.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n/useTranslation.ts":
/*!****************************************!*\
  !*** ./src/lib/i18n/useTranslation.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLanguageLabel: () => (/* binding */ getLanguageLabel),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./translations */ \"(ssr)/./src/lib/i18n/translations.ts\");\n\nconst useTranslation = (language = 'en')=>{\n    const t = (key)=>{\n        const translation = _translations__WEBPACK_IMPORTED_MODULE_0__.translations[language]?.[key];\n        if (translation) {\n            return translation;\n        }\n        // Fallback to English if translation not found\n        const fallback = _translations__WEBPACK_IMPORTED_MODULE_0__.translations.en[key];\n        if (fallback) {\n            return fallback;\n        }\n        // Return the key if no translation found\n        return key;\n    };\n    return {\n        t\n    };\n};\nconst getLanguageLabel = (code)=>{\n    const languageLabels = {\n        en: 'US English',\n        es: 'ES Español',\n        fr: 'FR Français',\n        de: 'DE Deutsch',\n        it: 'IT Italiano',\n        pt: 'PT Português',\n        nl: 'NL Nederlands',\n        zh: 'CN 中文',\n        ja: 'JP 日本語',\n        ko: 'KR 한국어',\n        ru: 'RU Русский',\n        gr: 'GR Ελληνικά',\n        he: 'IL עברית',\n        th: 'TH ไทย',\n        vi: 'VN Tiếng Việt',\n        id: 'ID Bahasa Indonesia'\n    };\n    return languageLabels[code] || code.toUpperCase();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n/useTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://arkywiwduoqpobflwssm.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFya3l3aXdkdW9xcG9iZmx3c3NtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDQ4NTMsImV4cCI6MjA2Njg4MDg1M30.rSxpWBHF3-6XqghqDPGGeDolieVdUBmKP0-oAsWHpUY\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce'\n    }\n});\n// Auth helper functions\nconst auth = {\n    // Sign up with email and password\n    signUp: async (email, password, userData)=>{\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData,\n                emailRedirectTo: `${window.location.origin}/auth/callback`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign in with email and password\n    signIn: async (email, password)=>{\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign in with magic link\n    signInWithMagicLink: async (email)=>{\n        const { data, error } = await supabase.auth.signInWithOtp({\n            email,\n            options: {\n                emailRedirectTo: `${window.location.origin}/auth/callback`\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign in with Google OAuth\n    signInWithGoogle: async ()=>{\n        const { data, error } = await supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: `${window.location.origin}/auth/callback`,\n                queryParams: {\n                    access_type: 'offline',\n                    prompt: 'consent'\n                }\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign out\n    signOut: async ()=>{\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    },\n    // Get current session\n    getSession: async ()=>{\n        const { data: { session }, error } = await supabase.auth.getSession();\n        return {\n            session,\n            error\n        };\n    },\n    // Get current user\n    getUser: async ()=>{\n        const { data: { user }, error } = await supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    },\n    // Listen to auth changes\n    onAuthStateChange: (callback)=>{\n        return supabase.auth.onAuthStateChange(callback);\n    },\n    // Reset password\n    resetPassword: async (email)=>{\n        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Update password\n    updatePassword: async (password)=>{\n        const { data, error } = await supabase.auth.updateUser({\n            password\n        });\n        return {\n            data,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/lucide-react","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cchick%5CDocuments%5Caugment-projects%5Ckoi-app3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();