'use client';

import { useState } from 'react';
import { 
  Home, 
  StickyNote, 
  CheckSquare, 
  Calendar, 
  Mail, 
  Users, 
  BookOpen, 
  Heart, 
  Globe, 
  Lightbulb, 
  Smile, 
  Users2, 
  Trophy, 
  ChefHat, 
  Camera, 
  BookOpenCheck, 
  Gamepad2, 
  Newspaper,
  Menu,
  X,
  Bot,
  Zap
} from 'lucide-react';
import { clsx } from 'clsx';

interface NavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const Navigation = ({ activeSection, onSectionChange }: NavigationProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationItems = [
    { id: 'dashboard', name: 'Dashboard', icon: Home, category: 'main' },
    
    // Productivity
    { id: 'notes', name: 'Notes', icon: StickyNote, category: 'productivity' },
    { id: 'tasks', name: 'Tasks', icon: CheckSquare, category: 'productivity' },
    { id: 'content', name: 'Content Creator', icon: Lightbulb, category: 'productivity' },
    { id: 'calendar', name: 'Calendar', icon: Calendar, category: 'productivity' },
    { id: 'email', name: 'Email', icon: Mail, category: 'productivity' },
    { id: 'contacts', name: 'Contacts', icon: Users, category: 'productivity' },
    { id: 'diary', name: 'Daily Diary', icon: BookOpen, category: 'productivity' },
    { id: 'health', name: 'Healthy Living', icon: Heart, category: 'productivity' },
    { id: 'websites', name: 'Favorite Websites', icon: Globe, category: 'productivity' },
    
    // Fun
    { id: 'facts', name: 'Fun Facts & Quotes', icon: Smile, category: 'fun' },
    { id: 'excuses', name: 'Excuse Generator', icon: Lightbulb, category: 'fun' },
    { id: 'companions', name: 'Companions', icon: Users2, category: 'fun' },
    { id: 'achievements', name: 'My Achievements', icon: Trophy, category: 'fun' },
    { id: 'recipes', name: 'Recipes', icon: ChefHat, category: 'fun' },
    { id: 'pictures', name: 'My Pictures', icon: Camera, category: 'fun' },
    { id: 'stories', name: 'KOI Adventure Stories', icon: BookOpenCheck, category: 'fun' },
    { id: 'games', name: 'Mini Games', icon: Gamepad2, category: 'fun' },
    { id: 'news', name: 'Latest News', icon: Newspaper, category: 'fun' },
    
    // Gaming
    { id: 'levelup', name: 'Level Up Game', icon: Zap, category: 'gaming' },
  ];

  const categories = {
    main: { name: 'Main', color: 'text-gray-600' },
    productivity: { name: 'Productivity', color: 'text-blue-600' },
    fun: { name: 'Fun Activities', color: 'text-purple-600' },
    gaming: { name: 'Gaming', color: 'text-yellow-600' }
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-md"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Sidebar */}
      <aside className={clsx(
        'fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0',
        isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center space-x-3 p-6 border-b border-gray-200">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">🐠</span>
            </div>
            <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Koi Hub
            </h1>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4">
            {Object.entries(categories).map(([categoryKey, category]) => (
              <div key={categoryKey} className="mb-6">
                <h3 className={clsx('text-xs font-semibold uppercase tracking-wider mb-3', category.color)}>
                  {category.name}
                </h3>
                <ul className="space-y-1">
                  {navigationItems
                    .filter(item => item.category === categoryKey)
                    .map((item) => (
                      <li key={item.id}>
                        <button
                          onClick={() => {
                            onSectionChange(item.id);
                            setIsMobileMenuOpen(false);
                          }}
                          className={clsx(
                            'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors',
                            activeSection === item.id
                              ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-500'
                              : 'text-gray-700 hover:bg-gray-50'
                          )}
                        >
                          <item.icon className="w-5 h-5" />
                          <span className="text-sm font-medium">{item.name}</span>
                        </button>
                      </li>
                    ))}
                </ul>
              </div>
            ))}
          </nav>

          {/* AI Assistant Button */}
          <div className="p-4 border-t border-gray-200">
            <button className="w-full flex items-center space-x-3 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all">
              <Bot className="w-5 h-5" />
              <span className="font-medium">AI Assistant</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </>
  );
};

export default Navigation;
