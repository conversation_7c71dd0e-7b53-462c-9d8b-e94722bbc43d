// Database utility functions for Koi Joyful Habits Hub
import { supabase } from './supabase';
import type {
  Task, ContentPiece, CalendarEvent, Contact, DiaryEntry, HealthEntry,
  FavoriteWebsite, Quest, Companion, Recipe, Picture, FactQuote, Excuse,
  Story, Game, GameSession, NewsSource, NewsArticle, EmailTemplate,
  EmailDraft, UserPreference, AIConversation, SharedContent
} from './supabase';

// Generic CRUD operations
export class DatabaseService<T extends { id: string; user_id: string }> {
  constructor(private tableName: string) {}

  async create(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: T | null; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data: result, error } = await supabase
      .from(this.tableName)
      .insert([data])
      .select()
      .single();
    return { data: result, error };
  }

  async getAll(userId: string, filters?: any): Promise<{ data: T[] | null; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    let query = supabase
      .from(this.tableName)
      .select('*')
      .eq('user_id', userId);

    // Apply soft delete filter if table has deleted_at column
    if (this.tableName !== 'xp_transactions' && this.tableName !== 'daily_checkins') {
      query = query.is('deleted_at', null);
    }

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });
    }

    const { data, error } = await query.order('updated_at', { ascending: false });
    return { data, error };
  }

  async getById(id: string): Promise<{ data: T | null; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .single();
    return { data, error };
  }

  async update(id: string, updates: Partial<T>): Promise<{ data: T | null; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .from(this.tableName)
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  }

  async delete(id: string, hardDelete = false): Promise<{ data: any; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    if (hardDelete) {
      const { data, error } = await supabase
        .from(this.tableName)
        .delete()
        .eq('id', id);
      return { data, error };
    } else {
      // Soft delete
      const { data, error } = await supabase
        .from(this.tableName)
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', id);
      return { data, error };
    }
  }

  async search(userId: string, searchTerm: string, searchFields: string[]): Promise<{ data: T[] | null; error: any }> {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    let query = supabase
      .from(this.tableName)
      .select('*')
      .eq('user_id', userId)
      .is('deleted_at', null);

    // Build OR condition for searching across multiple fields
    const orConditions = searchFields.map(field => `${field}.ilike.%${searchTerm}%`).join(',');
    query = query.or(orConditions);

    const { data, error } = await query.order('updated_at', { ascending: false });
    return { data, error };
  }
}

// Specific service instances
export const notesService = new DatabaseService<any>('notes');
export const tasksService = new DatabaseService<Task>('tasks');
export const contentService = new DatabaseService<ContentPiece>('content_pieces');
export const calendarService = new DatabaseService<CalendarEvent>('calendar_events');
export const contactsService = new DatabaseService<Contact>('contacts');
export const diaryService = new DatabaseService<DiaryEntry>('diary_entries');
export const healthService = new DatabaseService<HealthEntry>('health_entries');
export const websitesService = new DatabaseService<FavoriteWebsite>('favorite_websites');
export const questsService = new DatabaseService<Quest>('quests');
export const companionsService = new DatabaseService<Companion>('companions');
export const recipesService = new DatabaseService<Recipe>('recipes');
export const picturesService = new DatabaseService<Picture>('pictures');
export const factsQuotesService = new DatabaseService<FactQuote>('facts_quotes');
export const excusesService = new DatabaseService<Excuse>('excuses');
export const storiesService = new DatabaseService<Story>('stories');
export const gamesService = new DatabaseService<Game>('games');
export const gameSessionsService = new DatabaseService<GameSession>('game_sessions');
export const newsSourcesService = new DatabaseService<NewsSource>('news_sources');
export const newsArticlesService = new DatabaseService<NewsArticle>('news_articles');
export const emailTemplatesService = new DatabaseService<EmailTemplate>('email_templates');
export const emailDraftsService = new DatabaseService<EmailDraft>('email_drafts');
export const userPreferencesService = new DatabaseService<UserPreference>('user_preferences');
export const aiConversationsService = new DatabaseService<AIConversation>('ai_conversations');
export const sharedContentService = new DatabaseService<SharedContent>('shared_content');

// Specialized functions for complex operations

// Gaming system functions
export const gamingService = {
  async performDailyCheckin(userId: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .rpc('perform_daily_checkin', { p_user_id: userId });
    return { data, error };
  },

  async awardXP(userId: string, amount: number, reason: string, category = 'general', referenceId?: string, referenceType?: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .rpc('award_xp', {
        p_user_id: userId,
        p_amount: amount,
        p_reason: reason,
        p_category: category,
        p_reference_id: referenceId,
        p_reference_type: referenceType
      });
    return { data, error };
  },

  async getUserStats(userId: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('level, xp, daily_streak, coins')
      .eq('id', userId)
      .single();

    if (userError) return { data: null, error: userError };

    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select('count')
      .eq('user_id', userId);

    const { data: completedQuests, error: questsError } = await supabase
      .from('quests')
      .select('count')
      .eq('user_id', userId)
      .eq('completed', true);

    return {
      data: {
        ...user,
        total_achievements: achievements?.length || 0,
        completed_quests: completedQuests?.length || 0
      },
      error: null
    };
  }
};

// Folder management
export const folderService = {
  async getFolders(userId: string, tableName: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .from(tableName)
      .select('folder')
      .eq('user_id', userId)
      .is('deleted_at', null);

    if (error) return { data: null, error };

    const folders = [...new Set(data?.map(item => item.folder) || [])];
    return { data: folders, error: null };
  },

  async getItemsByFolder(userId: string, tableName: string, folder: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .eq('user_id', userId)
      .eq('folder', folder)
      .is('deleted_at', null)
      .order('updated_at', { ascending: false });

    return { data, error };
  }
};

// Search across multiple tables
export const searchService = {
  async globalSearch(userId: string, searchTerm: string) {
    if (!supabase) return { data: null, error: new Error('Supabase not configured') };
    
    const searchPromises = [
      notesService.search(userId, searchTerm, ['title', 'content']),
      tasksService.search(userId, searchTerm, ['title', 'description']),
      contentService.search(userId, searchTerm, ['title', 'content']),
      contactsService.search(userId, searchTerm, ['name', 'email', 'company']),
      recipesService.search(userId, searchTerm, ['title', 'description']),
      storiesService.search(userId, searchTerm, ['title', 'content'])
    ];

    const results = await Promise.allSettled(searchPromises);
    
    const searchResults = {
      notes: [],
      tasks: [],
      content: [],
      contacts: [],
      recipes: [],
      stories: []
    };

    const tableNames = ['notes', 'tasks', 'content', 'contacts', 'recipes', 'stories'];
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.data) {
        searchResults[tableNames[index] as keyof typeof searchResults] = result.value.data;
      }
    });

    return { data: searchResults, error: null };
  }
};
