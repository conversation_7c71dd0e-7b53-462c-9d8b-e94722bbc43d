# 🤖 AI Assistant Setup Guide

## Overview
Your Koi Joyful Habits Hub includes a powerful AI Assistant powered by DeepSeek, providing context-aware help across all features.

## ✅ What's Already Built

### 🔧 **Complete AI Infrastructure**
- ✅ **DeepSeek API Integration** - Full API route with error handling
- ✅ **Context-Aware Prompts** - Different AI personalities for each feature
- ✅ **Beautiful Chat Interface** - Koi-themed floating chat window
- ✅ **Feature Integration** - AI buttons in Notes, Tasks, and more
- ✅ **Multi-language Support** - AI responds in user's selected language

### 🎯 **AI Features Available**
- **Notes Assistant** - Help with note organization, writing, and structure
- **Tasks Assistant** - Productivity tips, task breakdown, and prioritization
- **Content Creator** - Creative writing assistance and brainstorming
- **Calendar Assistant** - Scheduling optimization and time management
- **Health Assistant** - Wellness guidance and habit tracking
- **Recipe Assistant** - Cooking tips and meal planning
- **General Assistant** - Overall productivity and life guidance

## 🚀 Quick Setup (2 minutes)

### Step 1: Get DeepSeek API Key
1. **Visit DeepSeek Platform**
   - Go to: https://platform.deepseek.com
   - Click "Sign Up" or "Log In"

2. **Create Account**
   - Sign up with email or GitHub
   - Verify your email address

3. **Get API Key**
   - Go to "API Keys" section
   - Click "Create API Key"
   - Copy your API key (starts with `sk-`)

### Step 2: Add API Key to Your App
1. **Open your `.env.local` file**
2. **Replace the placeholder:**
   ```env
   # Change this line:
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   
   # To your actual key:
   DEEPSEEK_API_KEY=sk-your-actual-api-key-here
   ```
3. **Save the file**

### Step 3: Restart Development Server
```bash
# Stop the server (Ctrl+C in terminal)
# Then restart:
npm run dev
```

### Step 4: Test AI Assistant
1. **Go to your app** (http://localhost:3000)
2. **Navigate to Notes or Tasks**
3. **Click "AI Assistant" button**
4. **Ask a question** like "Help me organize my tasks"
5. **You should get an intelligent response!**

## 🎯 AI Assistant Features

### **Context-Aware Help**
The AI knows which feature you're using and provides relevant assistance:

- **In Notes**: "Help me structure this meeting note"
- **In Tasks**: "Break down this project into smaller tasks"
- **In Calendar**: "Suggest the best time for this meeting"
- **In Health**: "Create a workout plan for beginners"

### **Smart Suggestions**
- Template recommendations
- Productivity tips
- Content ideas
- Organization strategies

### **Multi-language Support**
The AI responds in your selected language and understands context in any language.

## 🔧 Advanced Configuration

### **Custom Prompts**
You can modify AI behavior in `src/app/api/ai/chat/route.ts`:

```typescript
const systemPrompts = {
  notes: "Your custom note-taking assistant prompt...",
  tasks: "Your custom task management prompt...",
  // Add more custom prompts
};
```

### **API Settings**
Adjust AI behavior in the API route:
- `max_tokens`: Response length (default: 500)
- `temperature`: Creativity level (0.0-1.0, default: 0.7)
- `model`: DeepSeek model (default: 'deepseek-chat')

### **Rate Limiting**
The API includes built-in error handling and rate limiting for production use.

## 🎉 What You Can Do Now

### **Productivity Boost**
- Get writing assistance for notes
- Break down complex projects
- Optimize your daily schedule
- Get personalized productivity tips

### **Creative Help**
- Brainstorm content ideas
- Improve your writing
- Plan creative projects
- Get inspiration for goals

### **Learning Support**
- Ask questions about any topic
- Get explanations and tutorials
- Learn new productivity techniques
- Understand complex concepts

## 🔍 Troubleshooting

### **AI Not Responding?**
1. Check your API key is correct in `.env.local`
2. Restart the development server
3. Check browser console for errors
4. Verify DeepSeek account has credits

### **Getting Error Messages?**
- **"API key not configured"** - Add your DeepSeek API key
- **"Failed to get AI response"** - Check internet connection and API key
- **"Rate limit exceeded"** - Wait a moment and try again

### **Want Different AI Behavior?**
- Modify the system prompts in the API route
- Adjust temperature for more/less creativity
- Change max_tokens for longer/shorter responses

## 💡 Pro Tips

### **Best Practices**
- Be specific in your questions
- Provide context for better answers
- Use the feature-specific AI for best results
- Ask follow-up questions for clarification

### **Example Prompts**
- "Help me create a weekly meal plan"
- "Break down 'Launch new website' into actionable tasks"
- "Suggest a note-taking template for client meetings"
- "What's the best way to track my fitness goals?"

## 🎊 You're All Set!

Your AI Assistant is now ready to help you be more productive, creative, and organized. The AI learns from context and provides personalized assistance for every aspect of your Koi Joyful Habits Hub experience.

**Next Steps:**
1. Add your DeepSeek API key
2. Restart the server
3. Start chatting with your AI assistant
4. Explore all the different AI personalities across features

Happy productivity! 🐠✨
