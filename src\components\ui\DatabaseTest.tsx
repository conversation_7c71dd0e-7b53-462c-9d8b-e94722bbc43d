'use client';

import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import Button from './Button';
import Card from './Card';
import { Database, CheckCircle, XCircle, Loader2 } from 'lucide-react';

const DatabaseTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    details?: any;
  } | null>(null);

  const testConnection = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Check if Supabase is configured
      if (!supabase) {
        setResult({
          success: false,
          message: 'Supabase not configured',
          details: 'Please add your Supabase credentials to .env.local'
        });
        return;
      }

      // Test basic connection
      const { error } = await supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        setResult({
          success: false,
          message: 'Database connection failed',
          details: error.message
        });
      } else {
        setResult({
          success: true,
          message: 'Database connection successful!',
          details: 'Successfully connected to Supabase'
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Connection error',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card padding="md" className="max-w-md">
      <div className="text-center">
        <Database className="w-12 h-12 mx-auto mb-4 text-blue-500" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Database Connection Test
        </h3>
        <p className="text-gray-600 text-sm mb-4">
          Test your Supabase database connection
        </p>
        
        <Button 
          onClick={testConnection} 
          disabled={isLoading}
          className="w-full mb-4"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              <Database className="w-4 h-4 mr-2" />
              Test Connection
            </>
          )}
        </Button>

        {result && (
          <div className={`p-3 rounded-lg text-sm ${
            result.success 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center justify-center mb-2">
              {result.success ? (
                <CheckCircle className="w-5 h-5 mr-2" />
              ) : (
                <XCircle className="w-5 h-5 mr-2" />
              )}
              <span className="font-medium">{result.message}</span>
            </div>
            {result.details && (
              <p className="text-xs opacity-75">{result.details}</p>
            )}
          </div>
        )}

        <div className="mt-4 text-xs text-gray-500">
          <p>Make sure you&apos;ve:</p>
          <ul className="text-left mt-2 space-y-1">
            <li>• Added Supabase credentials to .env.local</li>
            <li>• Run the database setup SQL scripts</li>
            <li>• Restarted your development server</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default DatabaseTest;
