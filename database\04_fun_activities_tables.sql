-- Fun Activities Tables for Koi Joyful Habits Hub

-- Facts and Quotes table
CREATE TABLE facts_quotes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('fact', 'quote', 'tip')),
  content TEXT NOT NULL,
  author TEXT,
  category TEXT DEFAULT 'general',
  tags JSONB DEFAULT '[]',
  source_url TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  is_custom BOOLEAN DEFAULT FALSE, -- user-created vs system-provided
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Excuse Generator table
CREATE TABLE excuses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  category TEXT NOT NULL DEFAULT 'general', -- 'work', 'social', 'exercise', 'general'
  excuse_text TEXT NOT NULL,
  is_custom BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Stories table
CREATE TABLE stories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  genre TEXT DEFAULT 'general',
  tags JSONB DEFAULT '[]',
  is_favorite BOOLEAN DEFAULT FALSE,
  reading_time INTEGER, -- estimated reading time in minutes
  word_count INTEGER,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  folder TEXT DEFAULT 'My Stories',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Games table
CREATE TABLE games (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'puzzle', 'trivia', 'word', 'memory', 'strategy'
  description TEXT,
  high_score INTEGER DEFAULT 0,
  times_played INTEGER DEFAULT 0,
  last_played_at TIMESTAMP WITH TIME ZONE,
  settings JSONB DEFAULT '{}', -- game-specific settings
  achievements JSONB DEFAULT '[]', -- game-specific achievements
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Game Sessions table
CREATE TABLE game_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  game_id UUID REFERENCES games(id) ON DELETE CASCADE NOT NULL,
  score INTEGER DEFAULT 0,
  duration INTEGER, -- session duration in seconds
  completed BOOLEAN DEFAULT FALSE,
  session_data JSONB DEFAULT '{}', -- game state, moves, etc.
  played_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- News Sources table
CREATE TABLE news_sources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  is_active BOOLEAN DEFAULT TRUE,
  refresh_interval INTEGER DEFAULT 60, -- minutes
  last_fetched_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- News Articles table
CREATE TABLE news_articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  source_id UUID REFERENCES news_sources(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  url TEXT NOT NULL,
  image_url TEXT,
  published_at TIMESTAMP WITH TIME ZONE,
  category TEXT DEFAULT 'general',
  is_read BOOLEAN DEFAULT FALSE,
  is_favorite BOOLEAN DEFAULT FALSE,
  tags JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Templates table
CREATE TABLE email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  tags JSONB DEFAULT '[]',
  is_favorite BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Email Drafts table
CREATE TABLE email_drafts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  to_addresses JSONB NOT NULL DEFAULT '[]',
  cc_addresses JSONB DEFAULT '[]',
  bcc_addresses JSONB DEFAULT '[]',
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  attachments JSONB DEFAULT '[]',
  template_id UUID REFERENCES email_templates(id),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  is_sent BOOLEAN DEFAULT FALSE,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- User Preferences table
CREATE TABLE user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  category TEXT NOT NULL, -- 'theme', 'notifications', 'privacy', 'ai_assistant'
  key TEXT NOT NULL,
  value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, category, key)
);

-- AI Assistant Conversations table
CREATE TABLE ai_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  title TEXT,
  context TEXT, -- 'notes', 'tasks', 'general', 'creative', etc.
  messages JSONB NOT NULL DEFAULT '[]',
  is_pinned BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Shared Content table (for sharing features)
CREATE TABLE shared_content (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  content_type TEXT NOT NULL, -- 'note', 'task', 'recipe', 'story', etc.
  content_id UUID NOT NULL,
  share_token TEXT UNIQUE NOT NULL,
  permissions JSONB DEFAULT '{"view": true, "edit": false}',
  expires_at TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for all fun activities tables
ALTER TABLE facts_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE excuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_drafts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE shared_content ENABLE ROW LEVEL SECURITY;

-- RLS Policies for fun activities tables
CREATE POLICY "Users can manage own facts/quotes" ON facts_quotes
  FOR ALL USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can manage own excuses" ON excuses
  FOR ALL USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can manage own stories" ON stories
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own games" ON games
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own game sessions" ON game_sessions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own news sources" ON news_sources
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own news articles" ON news_articles
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own email templates" ON email_templates
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own email drafts" ON email_drafts
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own AI conversations" ON ai_conversations
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own shared content" ON shared_content
  FOR ALL USING (auth.uid() = user_id);

-- Special policy for shared content access via token
CREATE POLICY "Public can view shared content with valid token" ON shared_content
  FOR SELECT USING (
    expires_at IS NULL OR expires_at > NOW()
  );

-- Add updated_at triggers for tables that need them
CREATE TRIGGER update_facts_quotes_updated_at
  BEFORE UPDATE ON facts_quotes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_excuses_updated_at
  BEFORE UPDATE ON excuses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stories_updated_at
  BEFORE UPDATE ON stories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_games_updated_at
  BEFORE UPDATE ON games
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_sources_updated_at
  BEFORE UPDATE ON news_sources
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON email_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_drafts_updated_at
  BEFORE UPDATE ON email_drafts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_conversations_updated_at
  BEFORE UPDATE ON ai_conversations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Indexes for better performance
CREATE INDEX idx_facts_quotes_user_id ON facts_quotes(user_id);
CREATE INDEX idx_facts_quotes_type ON facts_quotes(type);
CREATE INDEX idx_facts_quotes_deleted_at ON facts_quotes(deleted_at);
CREATE INDEX idx_excuses_user_id ON excuses(user_id);
CREATE INDEX idx_excuses_category ON excuses(category);
CREATE INDEX idx_excuses_deleted_at ON excuses(deleted_at);
CREATE INDEX idx_stories_user_id ON stories(user_id);
CREATE INDEX idx_stories_status ON stories(status);
CREATE INDEX idx_stories_deleted_at ON stories(deleted_at);
CREATE INDEX idx_games_user_id ON games(user_id);
CREATE INDEX idx_games_type ON games(type);
CREATE INDEX idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX idx_news_sources_user_id ON news_sources(user_id);
CREATE INDEX idx_news_articles_user_id ON news_articles(user_id);
CREATE INDEX idx_news_articles_source_id ON news_articles(source_id);
CREATE INDEX idx_email_templates_user_id ON email_templates(user_id);
CREATE INDEX idx_email_templates_deleted_at ON email_templates(deleted_at);
CREATE INDEX idx_email_drafts_user_id ON email_drafts(user_id);
CREATE INDEX idx_email_drafts_deleted_at ON email_drafts(deleted_at);
CREATE INDEX idx_user_preferences_user_category ON user_preferences(user_id, category);
CREATE INDEX idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX idx_ai_conversations_deleted_at ON ai_conversations(deleted_at);
CREATE INDEX idx_shared_content_user_id ON shared_content(user_id);
CREATE INDEX idx_shared_content_token ON shared_content(share_token);
CREATE INDEX idx_shared_content_expires ON shared_content(expires_at);
