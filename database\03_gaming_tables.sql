-- Achievements table
CREATE TABLE achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('badge', 'award', 'trophy', 'sticker')),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  icon TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Quests table
CREATE TABLE quests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('daily', 'weekly', 'monthly', 'special')),
  category TEXT DEFAULT 'general', -- 'productivity', 'fun', 'social', etc.
  target_value INTEGER NOT NULL DEFAULT 1,
  current_value INTEGER DEFAULT 0,
  completed BOOLEAN DEFAULT FALSE,
  xp_reward INTEGER NOT NULL DEFAULT 50,
  coin_reward INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Companions table
CREATE TABLE companions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- 'koi', 'cat', 'dragon', etc.
  level INTEGER DEFAULT 1,
  happiness INTEGER DEFAULT 100 CHECK (happiness >= 0 AND happiness <= 100),
  items JSONB DEFAULT '[]', -- array of item IDs/names
  is_active BOOLEAN DEFAULT FALSE,
  unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipes table
CREATE TABLE recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  ingredients JSONB NOT NULL DEFAULT '[]',
  instructions JSONB NOT NULL DEFAULT '[]',
  prep_time INTEGER, -- in minutes
  cook_time INTEGER, -- in minutes
  servings INTEGER,
  difficulty TEXT DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard')),
  category TEXT DEFAULT 'main_course',
  tags JSONB DEFAULT '[]',
  image_url TEXT,
  source_url TEXT,
  folder TEXT DEFAULT 'My Recipes',
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Pictures table
CREATE TABLE pictures (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  title TEXT,
  description TEXT,
  file_url TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  folder TEXT DEFAULT 'General',
  tags JSONB DEFAULT '[]',
  taken_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- XP Transactions table (for tracking XP history)
CREATE TABLE xp_transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  amount INTEGER NOT NULL,
  reason TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  reference_id UUID, -- ID of related record (note, task, etc.)
  reference_type TEXT, -- 'note', 'task', 'quest', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily Check-ins table
CREATE TABLE daily_checkins (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  xp_earned INTEGER DEFAULT 50,
  streak_day INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Mystery Box openings table
CREATE TABLE mystery_box_openings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  reward_type TEXT NOT NULL, -- 'xp', 'coins', 'item', 'achievement'
  reward_value TEXT NOT NULL, -- JSON string with reward details
  opened_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for all gaming tables
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE companions ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE pictures ENABLE ROW LEVEL SECURITY;
ALTER TABLE xp_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_checkins ENABLE ROW LEVEL SECURITY;
ALTER TABLE mystery_box_openings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own achievements" ON achievements
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own quests" ON quests
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own companions" ON companions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own recipes" ON recipes
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own pictures" ON pictures
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own XP transactions" ON xp_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own checkins" ON daily_checkins
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view own mystery box openings" ON mystery_box_openings
  FOR SELECT USING (auth.uid() = user_id);

-- Functions for XP and leveling
CREATE OR REPLACE FUNCTION award_xp(
  p_user_id UUID,
  p_amount INTEGER,
  p_reason TEXT,
  p_category TEXT DEFAULT 'general',
  p_reference_id UUID DEFAULT NULL,
  p_reference_type TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
  old_level INTEGER;
  new_level INTEGER;
  current_xp INTEGER;
BEGIN
  -- Get current XP and level
  SELECT xp, level INTO current_xp, old_level
  FROM users WHERE id = p_user_id;
  
  -- Add XP transaction
  INSERT INTO xp_transactions (user_id, amount, reason, category, reference_id, reference_type)
  VALUES (p_user_id, p_amount, p_reason, p_category, p_reference_id, p_reference_type);
  
  -- Update user XP
  UPDATE users 
  SET xp = xp + p_amount,
      updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Calculate new level
  new_level := FLOOR((current_xp + p_amount) / 1000) + 1;
  
  -- Update level if changed
  IF new_level > old_level THEN
    UPDATE users SET level = new_level WHERE id = p_user_id;
    
    -- Award level up achievement
    INSERT INTO achievements (user_id, type, name, description, icon, category)
    VALUES (
      p_user_id,
      'badge',
      'Level ' || new_level || ' Reached!',
      'Congratulations on reaching level ' || new_level || '!',
      '🏆',
      'leveling'
    );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for daily check-in
CREATE OR REPLACE FUNCTION perform_daily_checkin(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  last_checkin DATE;
  current_streak INTEGER;
  new_streak INTEGER;
  xp_reward INTEGER := 50;
  result JSONB;
BEGIN
  -- Check if already checked in today
  IF EXISTS (
    SELECT 1 FROM daily_checkins 
    WHERE user_id = p_user_id AND date = CURRENT_DATE
  ) THEN
    RETURN jsonb_build_object('success', false, 'message', 'Already checked in today');
  END IF;
  
  -- Get current streak
  SELECT daily_streak, last_checkin INTO current_streak, last_checkin
  FROM users WHERE id = p_user_id;
  
  -- Calculate new streak
  IF last_checkin = CURRENT_DATE - INTERVAL '1 day' THEN
    new_streak := current_streak + 1;
  ELSE
    new_streak := 1;
  END IF;
  
  -- Bonus XP for streaks
  IF new_streak >= 7 THEN
    xp_reward := xp_reward + 25; -- Bonus for weekly streak
  END IF;
  
  IF new_streak >= 30 THEN
    xp_reward := xp_reward + 50; -- Bonus for monthly streak
  END IF;
  
  -- Record check-in
  INSERT INTO daily_checkins (user_id, date, xp_earned, streak_day)
  VALUES (p_user_id, CURRENT_DATE, xp_reward, new_streak);
  
  -- Update user
  UPDATE users 
  SET daily_streak = new_streak,
      last_checkin = CURRENT_DATE,
      updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Award XP
  PERFORM award_xp(p_user_id, xp_reward, 'Daily check-in', 'daily', NULL, 'checkin');
  
  result := jsonb_build_object(
    'success', true,
    'streak', new_streak,
    'xp_earned', xp_reward,
    'message', 'Check-in successful!'
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add updated_at triggers for gaming tables
CREATE TRIGGER update_recipes_updated_at
  BEFORE UPDATE ON recipes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pictures_updated_at
  BEFORE UPDATE ON pictures
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Indexes for performance
CREATE INDEX idx_achievements_user_id ON achievements(user_id);
CREATE INDEX idx_quests_user_id ON quests(user_id);
CREATE INDEX idx_quests_type ON quests(type);
CREATE INDEX idx_companions_user_id ON companions(user_id);
CREATE INDEX idx_xp_transactions_user_id ON xp_transactions(user_id);
CREATE INDEX idx_daily_checkins_user_date ON daily_checkins(user_id, date);
CREATE INDEX idx_recipes_user_id ON recipes(user_id);
CREATE INDEX idx_recipes_deleted_at ON recipes(deleted_at);
CREATE INDEX idx_pictures_user_id ON pictures(user_id);
CREATE INDEX idx_pictures_deleted_at ON pictures(deleted_at);
