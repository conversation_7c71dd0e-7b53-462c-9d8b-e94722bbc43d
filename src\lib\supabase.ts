import { createClient } from '@supabase/supabase-js';

// These would normally come from environment variables
// For now, we'll set up the structure for when you create your Supabase project
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Only create client if we have real values (not placeholders)
const hasValidConfig = supabaseUrl !== 'https://placeholder.supabase.co' &&
                      supabaseAnonKey !== 'placeholder-key' &&
                      !supabaseUrl.includes('your_supabase') &&
                      !supabaseAnonKey.includes('your_supabase');

export const supabase = hasValidConfig
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

// Database types matching our Supabase schema
export interface User {
  id: string;
  email: string;
  username?: string;
  avatar_url?: string;
  level: number;
  xp: number;
  daily_streak: number;
  coins: number;
  last_checkin?: string;
  current_visualization: string;
  created_at: string;
  updated_at: string;
}

export interface Note {
  id: string;
  user_id: string;
  title: string;
  content?: string;
  folder: string;
  tags: string[];
  due_date?: string;
  template_type?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Task {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  completed: boolean;
  folder: string;
  tags: string[];
  due_date?: string;
  recurring?: string;
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  completed_at?: string;
}

export interface ContentPiece {
  id: string;
  user_id: string;
  title: string;
  content?: string;
  type: string;
  folder: string;
  tags: string[];
  due_date?: string;
  template_type?: string;
  status: 'draft' | 'in_progress' | 'completed' | 'published';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CalendarEvent {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  all_day: boolean;
  location?: string;
  attendees: string[];
  source: string;
  external_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Contact {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  position?: string;
  folder: string;
  tags: string[];
  notes?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface DiaryEntry {
  id: string;
  user_id: string;
  date: string;
  title?: string;
  content: string;
  mood?: number;
  tags: string[];
  attachments: string[];
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface HealthEntry {
  id: string;
  user_id: string;
  date: string;
  type: 'workout' | 'nutrition' | 'goal' | 'measurement';
  title: string;
  description?: string;
  value?: number;
  unit?: string;
  category?: string;
  created_at: string;
  updated_at: string;
}

export interface FavoriteWebsite {
  id: string;
  user_id: string;
  title: string;
  url: string;
  description?: string;
  folder: string;
  tags: string[];
  favicon_url?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Achievement {
  id: string;
  user_id: string;
  type: 'badge' | 'award' | 'trophy' | 'sticker';
  name: string;
  description: string;
  icon: string;
  category: string;
  earned_at: string;
}

export interface Quest {
  id: string;
  user_id: string;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'monthly' | 'special';
  category: string;
  target_value: number;
  current_value: number;
  completed: boolean;
  xp_reward: number;
  coin_reward: number;
  completed_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface Companion {
  id: string;
  user_id: string;
  name: string;
  type: string;
  level: number;
  happiness: number;
  items: string[];
  is_active: boolean;
  unlocked_at: string;
  created_at: string;
}

export interface Recipe {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  ingredients: any[];
  instructions: any[];
  prep_time?: number;
  cook_time?: number;
  servings?: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  image_url?: string;
  source_url?: string;
  folder: string;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Picture {
  id: string;
  user_id: string;
  title?: string;
  description?: string;
  file_url: string;
  file_size?: number;
  mime_type?: string;
  folder: string;
  tags: string[];
  taken_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface XPTransaction {
  id: string;
  user_id: string;
  amount: number;
  reason: string;
  category: string;
  reference_id?: string;
  reference_type?: string;
  created_at: string;
}

export interface DailyCheckin {
  id: string;
  user_id: string;
  date: string;
  xp_earned: number;
  streak_day: number;
  created_at: string;
}

// Auth helpers
export const signUp = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

// Data helpers for Notes
export const createNote = async (note: Omit<Note, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('notes')
    .insert([note])
    .select()
    .single();
  return { data, error };
};

export const getNotes = async (userId: string) => {
  const { data, error } = await supabase
    .from('notes')
    .select('*')
    .eq('user_id', userId)
    .is('deleted_at', null)
    .order('updated_at', { ascending: false });
  return { data, error };
};

export const updateNote = async (id: string, updates: Partial<Note>) => {
  const { data, error } = await supabase
    .from('notes')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();
  return { data, error };
};

export const deleteNote = async (id: string) => {
  const { data, error } = await supabase
    .from('notes')
    .update({ deleted_at: new Date().toISOString() })
    .eq('id', id);
  return { data, error };
};

// Data helpers for Tasks
export const createTask = async (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('tasks')
    .insert([task])
    .select()
    .single();
  return { data, error };
};

export const getTasks = async (userId: string) => {
  const { data, error } = await supabase
    .from('tasks')
    .select('*')
    .eq('user_id', userId)
    .is('deleted_at', null)
    .order('updated_at', { ascending: false });
  return { data, error };
};

export const updateTask = async (id: string, updates: Partial<Task>) => {
  const { data, error } = await supabase
    .from('tasks')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();
  return { data, error };
};

export const deleteTask = async (id: string) => {
  const { data, error } = await supabase
    .from('tasks')
    .update({ deleted_at: new Date().toISOString() })
    .eq('id', id);
  return { data, error };
};

export const completeTask = async (id: string) => {
  const { data, error } = await supabase
    .rpc('complete_task', { p_task_id: id });
  return { data, error };
};

// Gaming system helpers
export const performDailyCheckin = async (userId: string) => {
  const { data, error } = await supabase
    .rpc('perform_daily_checkin', { p_user_id: userId });
  return { data, error };
};

export const awardXP = async (
  userId: string,
  amount: number,
  reason: string,
  category: string = 'general',
  referenceId?: string,
  referenceType?: string
) => {
  const { data, error } = await supabase
    .rpc('award_xp', {
      p_user_id: userId,
      p_amount: amount,
      p_reason: reason,
      p_category: category,
      p_reference_id: referenceId,
      p_reference_type: referenceType
    });
  return { data, error };
};

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
  return { data, error };
};

export const getAchievements = async (userId: string) => {
  const { data, error } = await supabase
    .from('achievements')
    .select('*')
    .eq('user_id', userId)
    .order('earned_at', { ascending: false });
  return { data, error };
};

export const getActiveQuests = async (userId: string) => {
  const { data, error } = await supabase
    .from('quests')
    .select('*')
    .eq('user_id', userId)
    .eq('completed', false)
    .order('created_at', { ascending: false });
  return { data, error };
};

// Additional interfaces for new tables
export interface FactQuote {
  id: string;
  user_id?: string;
  type: 'fact' | 'quote' | 'tip';
  content: string;
  author?: string;
  category: string;
  tags: string[];
  source_url?: string;
  is_favorite: boolean;
  is_custom: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Excuse {
  id: string;
  user_id?: string;
  category: string;
  excuse_text: string;
  is_custom: boolean;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Story {
  id: string;
  user_id: string;
  title: string;
  content: string;
  genre: string;
  tags: string[];
  is_favorite: boolean;
  reading_time?: number;
  word_count?: number;
  status: 'draft' | 'published' | 'archived';
  folder: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Game {
  id: string;
  user_id: string;
  name: string;
  type: string;
  description?: string;
  high_score: number;
  times_played: number;
  last_played_at?: string;
  settings: any;
  achievements: any[];
  created_at: string;
  updated_at: string;
}

export interface GameSession {
  id: string;
  user_id: string;
  game_id: string;
  score: number;
  duration?: number;
  completed: boolean;
  session_data: any;
  played_at: string;
}

export interface NewsSource {
  id: string;
  user_id: string;
  name: string;
  url: string;
  category: string;
  is_active: boolean;
  refresh_interval: number;
  last_fetched_at?: string;
  created_at: string;
  updated_at: string;
}

export interface NewsArticle {
  id: string;
  user_id: string;
  source_id?: string;
  title: string;
  description?: string;
  url: string;
  image_url?: string;
  published_at?: string;
  category: string;
  is_read: boolean;
  is_favorite: boolean;
  tags: string[];
  created_at: string;
}

export interface EmailTemplate {
  id: string;
  user_id: string;
  name: string;
  subject: string;
  body: string;
  category: string;
  tags: string[];
  is_favorite: boolean;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface EmailDraft {
  id: string;
  user_id: string;
  to_addresses: string[];
  cc_addresses: string[];
  bcc_addresses: string[];
  subject: string;
  body: string;
  attachments: any[];
  template_id?: string;
  scheduled_at?: string;
  is_sent: boolean;
  sent_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface UserPreference {
  id: string;
  user_id: string;
  category: string;
  key: string;
  value: any;
  created_at: string;
  updated_at: string;
}

export interface AIConversation {
  id: string;
  user_id: string;
  title?: string;
  context: string;
  messages: any[];
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface SharedContent {
  id: string;
  user_id: string;
  content_type: string;
  content_id: string;
  share_token: string;
  permissions: any;
  expires_at?: string;
  access_count: number;
  last_accessed_at?: string;
  created_at: string;
}
