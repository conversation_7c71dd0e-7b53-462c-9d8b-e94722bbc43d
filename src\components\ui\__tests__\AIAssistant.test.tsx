import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AIAssistant from '../AIAssistant'

// Mock fetch for API calls
global.fetch = jest.fn()

describe('AIAssistant Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        message: 'This is a test AI response',
      }),
    })
  })

  it('renders AI Assistant button initially', () => {
    render(<AIAssistant context="notes" />)
    
    expect(screen.getByText('AI Assistant')).toBeInTheDocument()
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  it('opens chat window when button is clicked', async () => {
    render(<AIAssistant context="notes" />)
    
    const button = screen.getByText('AI Assistant')
    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
      expect(screen.getByText(/Hi! I'm your AI assistant for notes/)).toBeInTheDocument()
    })
  })

  it('closes chat window when X button is clicked', async () => {
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Close chat
    const closeButton = screen.getByRole('button', { name: /close/i })
    fireEvent.click(closeButton)

    await waitFor(() => {
      expect(screen.queryByText('Koi AI Assistant')).not.toBeInTheDocument()
    })
  })

  it('sends message and receives AI response', async () => {
    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Type and send message
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Help me organize my notes')

    const sendButton = screen.getByRole('button', { name: /send/i })
    fireEvent.click(sendButton)

    // Check that message was sent
    await waitFor(() => {
      expect(screen.getByText('Help me organize my notes')).toBeInTheDocument()
    })

    // Check that AI response appears
    await waitFor(() => {
      expect(screen.getByText('This is a test AI response')).toBeInTheDocument()
    })

    // Verify API was called
    expect(global.fetch).toHaveBeenCalledWith('/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Help me organize my notes',
        context: 'Koi Joyful Habits Hub - productivity and wellness app',
        feature: 'notes'
      }),
    })
  })

  it('handles API errors gracefully', async () => {
    ;(global.fetch as jest.Mock).mockRejectedValue(new Error('API Error'))

    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Send message
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Test message')

    const sendButton = screen.getByRole('button', { name: /send/i })
    fireEvent.click(sendButton)

    // Check error message appears
    await waitFor(() => {
      expect(screen.getByText(/I'm sorry, I'm having trouble connecting/)).toBeInTheDocument()
    })
  })

  it('handles failed API response', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: false,
      status: 500,
    })

    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Send message
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Test message')

    const sendButton = screen.getByRole('button', { name: /send/i })
    fireEvent.click(sendButton)

    // Check error message appears
    await waitFor(() => {
      expect(screen.getByText(/I'm sorry, I'm having trouble connecting/)).toBeInTheDocument()
    })
  })

  it('disables send button when input is empty', async () => {
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    const sendButton = screen.getByRole('button', { name: /send/i })
    expect(sendButton).toBeDisabled()
  })

  it('shows loading state while sending message', async () => {
    // Mock a delayed response
    ;(global.fetch as jest.Mock).mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ message: 'Response' })
        }), 100)
      )
    )

    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Send message
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Test message')

    const sendButton = screen.getByRole('button', { name: /send/i })
    fireEvent.click(sendButton)

    // Check loading state
    expect(sendButton).toBeDisabled()
    expect(textarea).toBeDisabled()
  })

  it('supports different contexts', () => {
    render(<AIAssistant context="tasks" />)
    
    const button = screen.getByText('AI Assistant')
    fireEvent.click(button)

    expect(screen.getByText(/Hi! I'm your AI assistant for tasks/)).toBeInTheDocument()
  })

  it('uses custom placeholder when provided', () => {
    render(
      <AIAssistant 
        context="notes" 
        placeholder="Custom placeholder text" 
      />
    )
    
    const button = screen.getByText('AI Assistant')
    fireEvent.click(button)

    expect(screen.getByPlaceholderText('Custom placeholder text')).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<AIAssistant context="notes" className="custom-class" />)
    
    const button = screen.getByText('AI Assistant')
    expect(button.closest('button')).toHaveClass('custom-class')
  })

  it('handles Enter key to send message', async () => {
    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Type message and press Enter
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Test message{enter}')

    // Check that message was sent
    await waitFor(() => {
      expect(screen.getByText('Test message')).toBeInTheDocument()
    })
  })

  it('handles Shift+Enter for new line', async () => {
    const user = userEvent.setup()
    render(<AIAssistant context="notes" />)
    
    // Open chat
    const openButton = screen.getByText('AI Assistant')
    fireEvent.click(openButton)

    await waitFor(() => {
      expect(screen.getByText('Koi AI Assistant')).toBeInTheDocument()
    })

    // Type message with Shift+Enter
    const textarea = screen.getByPlaceholderText(/Ask me to help/i)
    await user.type(textarea, 'Line 1{shift}{enter}Line 2')

    expect(textarea).toHaveValue('Line 1\nLine 2')
  })
})
