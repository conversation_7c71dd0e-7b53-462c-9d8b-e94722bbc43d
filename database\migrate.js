#!/usr/bin/env node

/**
 * Database Migration Script for Koi Joyful Habits Hub
 * 
 * This script helps you set up your Supabase database by running all SQL migration files
 * in the correct order. It can be run locally or in CI/CD pipelines.
 * 
 * Usage:
 *   node database/migrate.js
 * 
 * Prerequisites:
 *   - Supabase CLI installed (npm install -g supabase)
 *   - Supabase project created
 *   - Environment variables configured
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const DATABASE_DIR = __dirname;
const MIGRATION_FILES = [
  '01_users_table.sql',
  '02_productivity_tables.sql',
  '03_gaming_tables.sql',
  '04_fun_activities_tables.sql'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    log('✅ Supabase CLI is installed', 'green');
  } catch (error) {
    log('❌ Supabase CLI is not installed. Please install it first:', 'red');
    log('   npm install -g supabase', 'yellow');
    process.exit(1);
  }

  // Check if we're in a Supabase project
  if (!fs.existsSync(path.join(process.cwd(), 'supabase'))) {
    log('❌ Not in a Supabase project directory. Please run:', 'red');
    log('   supabase init', 'yellow');
    process.exit(1);
  }

  log('✅ Prerequisites check passed', 'green');
}

function checkMigrationFiles() {
  log('📁 Checking migration files...', 'blue');
  
  const missingFiles = [];
  
  MIGRATION_FILES.forEach(filename => {
    const filepath = path.join(DATABASE_DIR, filename);
    if (!fs.existsSync(filepath)) {
      missingFiles.push(filename);
    }
  });

  if (missingFiles.length > 0) {
    log('❌ Missing migration files:', 'red');
    missingFiles.forEach(file => log(`   - ${file}`, 'red'));
    process.exit(1);
  }

  log(`✅ All ${MIGRATION_FILES.length} migration files found`, 'green');
}

function runMigration(filename) {
  log(`🚀 Running migration: ${filename}`, 'cyan');
  
  const filepath = path.join(DATABASE_DIR, filename);
  
  try {
    // Read the SQL file
    const sqlContent = fs.readFileSync(filepath, 'utf8');
    
    // Write to a temporary file for Supabase CLI
    const tempFile = path.join(DATABASE_DIR, 'temp_migration.sql');
    fs.writeFileSync(tempFile, sqlContent);
    
    // Run the migration using Supabase CLI
    execSync(`supabase db reset --db-url postgresql://postgres:postgres@localhost:54322/postgres`, { 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    // Clean up temp file
    if (fs.existsSync(tempFile)) {
      fs.unlinkSync(tempFile);
    }
    
    log(`✅ Migration completed: ${filename}`, 'green');
    
  } catch (error) {
    log(`❌ Migration failed: ${filename}`, 'red');
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

function runAllMigrations() {
  log('🎯 Starting database migration...', 'bright');
  log(`📊 Total migrations to run: ${MIGRATION_FILES.length}`, 'blue');
  
  MIGRATION_FILES.forEach((filename, index) => {
    log(`\n[${index + 1}/${MIGRATION_FILES.length}] Processing ${filename}...`, 'magenta');
    runMigration(filename);
  });
  
  log('\n🎉 All migrations completed successfully!', 'green');
}

function showPostMigrationInstructions() {
  log('\n📋 Next Steps:', 'bright');
  log('1. Configure your environment variables in .env.local:', 'yellow');
  log('   - NEXT_PUBLIC_SUPABASE_URL', 'cyan');
  log('   - NEXT_PUBLIC_SUPABASE_ANON_KEY', 'cyan');
  log('   - SUPABASE_SERVICE_ROLE_KEY', 'cyan');
  
  log('\n2. Test your database connection:', 'yellow');
  log('   - Start your Next.js app: npm run dev', 'cyan');
  log('   - Try creating a user account', 'cyan');
  log('   - Test basic CRUD operations', 'cyan');
  
  log('\n3. Optional configurations:', 'yellow');
  log('   - Set up email authentication in Supabase dashboard', 'cyan');
  log('   - Configure social login providers', 'cyan');
  log('   - Set up storage buckets for file uploads', 'cyan');
  log('   - Configure real-time subscriptions', 'cyan');
  
  log('\n4. Verify RLS policies:', 'yellow');
  log('   - Test that users can only access their own data', 'cyan');
  log('   - Verify shared content policies work correctly', 'cyan');
  
  log('\n📚 Documentation:', 'bright');
  log('   - Supabase docs: https://supabase.com/docs', 'cyan');
  log('   - Database schema: database/00_setup_script.sql', 'cyan');
  log('   - API reference: src/lib/supabase.ts', 'cyan');
}

function main() {
  log('🐠 Koi Joyful Habits Hub - Database Migration', 'bright');
  log('=' .repeat(50), 'blue');
  
  try {
    checkPrerequisites();
    checkMigrationFiles();
    runAllMigrations();
    showPostMigrationInstructions();
    
  } catch (error) {
    log(`\n💥 Migration failed with error:`, 'red');
    log(error.message, 'red');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('Koi Joyful Habits Hub - Database Migration Script', 'bright');
  log('\nUsage:', 'yellow');
  log('  node database/migrate.js          Run all migrations', 'cyan');
  log('  node database/migrate.js --help   Show this help message', 'cyan');
  log('\nPrerequisites:', 'yellow');
  log('  - Supabase CLI installed (npm install -g supabase)', 'cyan');
  log('  - Supabase project initialized (supabase init)', 'cyan');
  log('  - Database running (supabase start)', 'cyan');
  process.exit(0);
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = {
  runAllMigrations,
  checkPrerequisites,
  checkMigrationFiles
};
