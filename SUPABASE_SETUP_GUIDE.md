# Koi Joyful Habits Hub - Supabase Database Setup Guide

This guide will walk you through setting up the comprehensive Supabase database for your Koi Joyful Habits Hub application.

## 📊 Database Overview

Your database includes **29 tables** organized into 4 main categories:

### 🔐 Users & Authentication (1 table)
- `users` - User profiles with gaming features (level, XP, streak, coins)

### 📝 Productivity Features (8 tables)
- `notes` - Personal and work notes with folders and tags
- `tasks` - Task management with priorities and recurring options
- `content_pieces` - Content creation and management
- `calendar_events` - Calendar integration and event management
- `contacts` - Contact management with folders
- `diary_entries` - Daily diary with mood tracking
- `health_entries` - Health and fitness tracking
- `favorite_websites` - Bookmark management

### 🎮 Gaming Features (8 tables)
- `achievements` - Badge/trophy system
- `quests` - Daily/weekly/monthly challenges
- `companions` - Virtual pets/companions
- `recipes` - Recipe collection and management
- `pictures` - Photo gallery with organization
- `xp_transactions` - XP history and tracking
- `daily_checkins` - Daily login rewards
- `mystery_box_openings` - Reward system

### 🎉 Fun Activities (12 tables)
- `facts_quotes` - Daily facts, quotes, and tips
- `excuses` - Excuse generator for various situations
- `stories` - Creative writing and story collection
- `games` - Game library and high scores
- `game_sessions` - Individual game session tracking
- `news_sources` - News feed configuration
- `news_articles` - News article storage and reading status
- `email_templates` - Email template management
- `email_drafts` - Email composition and scheduling
- `user_preferences` - User settings and preferences
- `ai_conversations` - AI assistant chat history
- `shared_content` - Content sharing system

## 🚀 Quick Setup (Recommended)

### Step 1: Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Create a new organization (if needed)
4. Create a new project
5. Choose a database password (save this!)
6. Wait for project to be ready (~2 minutes)

### Step 2: Get Your Project Credentials
1. In your Supabase dashboard, go to Settings → API
2. Copy your project URL and anon key
3. Copy your service role key (for admin operations)

### Step 3: Configure Environment Variables
1. Copy `.env.example` to `.env.local`
2. Fill in your Supabase credentials:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

### Step 4: Run Database Migrations
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Run the SQL files in this order:
   - `database/01_users_table.sql`
   - `database/02_productivity_tables.sql`
   - `database/03_gaming_tables.sql`
   - `database/04_fun_activities_tables.sql`

### Step 5: Test Your Setup
1. Start your Next.js app: `npm run dev`
2. Try creating a user account
3. Test basic functionality

## 🛠️ Advanced Setup (CLI Method)

If you prefer using the Supabase CLI:

### Prerequisites
```bash
npm install -g supabase
```

### Initialize Supabase
```bash
supabase init
supabase start
```

### Run Migrations
```bash
node database/migrate.js
```

## 🔒 Security Features

### Row Level Security (RLS)
- ✅ Enabled on all tables
- ✅ Users can only access their own data
- ✅ Shared content has special access policies
- ✅ System content (facts/quotes) accessible to all users

### Authentication
- ✅ Email/password authentication
- ✅ Automatic user profile creation
- ✅ Social login ready (configure in dashboard)
- ✅ Email confirmation (optional)

## 🎯 Key Features

### Gaming System
- **XP & Leveling**: Users earn XP and level up automatically
- **Daily Streaks**: Reward consecutive daily logins
- **Achievements**: Automatic badge/trophy system
- **Quests**: Daily, weekly, and monthly challenges
- **Companions**: Virtual pets with happiness system

### Productivity Tools
- **Smart Organization**: Folders and tags for all content
- **Soft Deletes**: Recover accidentally deleted items
- **Full-Text Search**: Search across all your content
- **Calendar Integration**: Ready for external calendar sync
- **Health Tracking**: Comprehensive wellness monitoring

### Fun Activities
- **AI Assistant**: Chat history and context management
- **Content Creation**: Stories, recipes, and creative writing
- **News Aggregation**: Custom news sources and reading tracking
- **Email Management**: Templates and draft system
- **Games**: High scores and session tracking

## 📱 Real-time Features

Your database is configured for real-time updates:

```typescript
// Example: Listen for new notes
supabase
  .channel('notes')
  .on('postgres_changes', 
    { event: 'INSERT', schema: 'public', table: 'notes' },
    (payload) => console.log('New note:', payload)
  )
  .subscribe()
```

## 🔧 Customization

### Adding New Tables
1. Create SQL file in `database/` directory
2. Follow naming convention: `05_your_feature_tables.sql`
3. Include RLS policies and indexes
4. Add TypeScript interfaces to `src/lib/supabase.ts`
5. Create service in `src/lib/database.ts`

### Modifying Existing Tables
1. Create migration file with ALTER statements
2. Update TypeScript interfaces
3. Test thoroughly in development

## 🚨 Troubleshooting

### Common Issues

**"Supabase not configured" error**
- Check your environment variables
- Ensure `.env.local` exists and has correct values
- Restart your development server

**RLS policy errors**
- Verify user is authenticated
- Check policy conditions in SQL files
- Test with service role key for debugging

**Migration failures**
- Check SQL syntax in migration files
- Ensure proper order of execution
- Verify foreign key constraints

### Getting Help

1. Check the [Supabase documentation](https://supabase.com/docs)
2. Review the database schema in `database/00_setup_script.sql`
3. Examine the API helpers in `src/lib/supabase.ts`
4. Test with the database utilities in `src/lib/database.ts`

## 🎉 Next Steps

After successful setup:

1. **Test Core Features**: Create notes, tasks, and user profile
2. **Configure Authentication**: Set up email templates and social providers
3. **Set up Storage**: Configure buckets for file uploads
4. **Add Real-time**: Implement live updates for collaborative features
5. **Monitor Performance**: Set up database monitoring and alerts
6. **Plan Backups**: Configure automated backups and recovery

## 📚 Additional Resources

- [Supabase Dashboard](https://app.supabase.com)
- [Database Schema Documentation](database/00_setup_script.sql)
- [API Reference](src/lib/supabase.ts)
- [Database Utilities](src/lib/database.ts)
- [Environment Configuration](.env.example)

---

🐠 **Happy coding with your Koi Joyful Habits Hub!** 🐠
