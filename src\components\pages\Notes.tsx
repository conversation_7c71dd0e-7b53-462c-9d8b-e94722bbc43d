'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Folder, Tag, Calendar, Share2, Trash2, Edit3, <PERSON>t, X, Save, ArrowLeft } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import AIAssistant from '../ui/AIAssistant';
import { useAuth } from '../../contexts/AuthContext';
import { createNote, getNotes, updateNote, deleteNote, awardXP, type Note } from '../../lib/supabase';

interface NotesProps {
  onBack?: () => void;
}

const Notes = ({ onBack }: NotesProps) => {
  const { user } = useAuth();
  const [notes, setNotes] = useState<Note[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [isCreating, setIsCreating] = useState(false);
  const [editingNote, setEditingNote] = useState<Note | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const folders = ['Personal', 'Work', 'Ideas', 'Projects'];
  const templates = [
    'Cornell Notes',
    'Outline',
    'Daily Journal',
    'Meeting Notes',
    'Zettelkasten',
    'Mind Map',
    'Reading Notes'
  ];

  // Load notes on component mount
  useEffect(() => {
    if (user) {
      loadNotes();
    }
  }, [user]);

  const loadNotes = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getNotes(user.id);
      if (error) {
        setError(error.message);
      } else {
        setNotes(data || []);
      }
    } catch (err) {
      setError('Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNote = async (template?: string) => {
    if (!user) return;

    const newNote = {
      user_id: user.id,
      title: template ? `${template} Note` : 'New Note',
      content: getTemplateContent(template),
      folder: 'Personal',
      tags: template ? [template.toLowerCase().replace(' ', '_')] : [],
      template_type: template
    };

    try {
      const { data, error } = await createNote(newNote);
      if (error) {
        setError(error.message);
      } else if (data) {
        setNotes(prev => [data, ...prev]);
        setEditingNote(data);

        // Award XP for creating a note
        await awardXP(user.id, 25, 'Created a note', 'productivity', data.id, 'note');
      }
    } catch (err) {
      setError('Failed to create note');
    }
  };

  const handleUpdateNote = async (noteId: string, updates: Partial<Note>) => {
    try {
      const { data, error } = await updateNote(noteId, updates);
      if (error) {
        setError(error.message);
      } else if (data) {
        setNotes(prev => prev.map(note => note.id === noteId ? data : note));
        setEditingNote(null);
      }
    } catch (err) {
      setError('Failed to update note');
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!confirm('Are you sure you want to delete this note?')) return;

    try {
      const { error } = await deleteNote(noteId);
      if (error) {
        setError(error.message);
      } else {
        setNotes(prev => prev.filter(note => note.id !== noteId));
      }
    } catch (err) {
      setError('Failed to delete note');
    }
  };

  const getTemplateContent = (template?: string): string => {
    switch (template) {
      case 'Cornell Notes':
        return '## Notes\n\n\n## Cue\n\n\n## Summary\n\n';
      case 'Daily Journal':
        return '## Today\'s Date: \n\n## What happened today?\n\n\n## How did I feel?\n\n\n## What am I grateful for?\n\n\n## Tomorrow\'s goals:\n\n';
      case 'Meeting Notes':
        return '## Meeting: \n**Date:** \n**Attendees:** \n\n## Agenda\n\n\n## Discussion\n\n\n## Action Items\n\n\n## Next Steps\n\n';
      default:
        return '';
    }
  };

  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (note.content || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFolder = selectedFolder === 'all' || note.folder.toLowerCase() === selectedFolder;
    return matchesSearch && matchesFolder;
  });

  // Show note editor if editing
  if (editingNote) {
    return <NoteEditor
      note={editingNote}
      onSave={handleUpdateNote}
      onCancel={() => setEditingNote(null)}
    />;
  }

  return (
    <div className="space-y-6">
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button
              onClick={onBack}
              variant="outline"
              className="border-blue-400/50 text-blue-200 hover:bg-blue-500/30"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold text-white">Notes</h1>
            <p className="text-blue-200">Create, organize, and manage your notes with AI assistance</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button onClick={() => handleCreateNote()}>
            <Plus className="w-4 h-4 mr-2" />
            New Note
          </Button>
          <AIAssistant
            context="notes"
            placeholder="Ask me to help with note-taking, organization, or content ideas..."
          />
        </div>
      </div>

      <Card>
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedFolder}
              onChange={(e) => setSelectedFolder(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Folders</option>
              {folders.map(folder => (
                <option key={folder} value={folder.toLowerCase()}>{folder}</option>
              ))}
            </select>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>
      </Card>

      {loading ? (
        <Card className="text-center py-12">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-blue-200">Loading your notes...</p>
        </Card>
      ) : filteredNotes.length === 0 ? (
        <Card className="text-center py-12 bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-blue-500/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Edit3 className="w-8 h-8 text-blue-300" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {notes.length === 0 ? 'No notes yet' : 'No matching notes'}
            </h3>
            <p className="text-blue-200 mb-6">
              {notes.length === 0
                ? 'Create your first note to get started on your productivity journey!'
                : 'Try adjusting your search or folder filter.'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={() => handleCreateNote()}>
                <Plus className="w-4 h-4 mr-2" />
                {notes.length === 0 ? 'Create First Note' : 'Create New Note'}
              </Button>
              <AIAssistant
                context="notes-empty"
                placeholder="Ask me for note-taking tips or templates..."
              />
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredNotes.map(note => (
            <Card key={note.id} className="hover:shadow-lg transition-shadow bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center space-x-2">
                  <Folder className="w-4 h-4 text-blue-300" />
                  <span className="text-sm text-blue-200 capitalize">{note.folder}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingNote(note)}
                    className="text-blue-300 hover:text-white hover:bg-blue-500/30"
                  >
                    <Edit3 className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteNote(note.id)}
                    className="text-red-300 hover:text-white hover:bg-red-500/30"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <h3 className="font-semibold text-white mb-2 line-clamp-2 cursor-pointer" onClick={() => setEditingNote(note)}>
                {note.title}
              </h3>
              <p className="text-blue-200 text-sm mb-4 line-clamp-3">
                {note.content || 'No content'}
              </p>
              <div className="flex flex-wrap gap-1 mb-3">
                {note.tags.map(tag => (
                  <span key={tag} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-400/30 text-blue-100">
                    <Tag className="w-3 h-3 mr-1" />
                    {tag}
                  </span>
                ))}
              </div>
              <div className="flex justify-between items-center text-xs text-blue-300">
                <span>Updated {new Date(note.updated_at).toLocaleDateString()}</span>
                {note.due_date && (
                  <span className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    Due {new Date(note.due_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Quick Templates</h3>
          <Button variant="outline" size="sm" className="border-blue-400/50 text-blue-200 hover:bg-blue-500/30">
            <Bot className="w-4 h-4 mr-2" />
            AI Suggest
          </Button>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-7 gap-3">
          {templates.map(template => (
            <Button
              key={template}
              variant="outline"
              size="sm"
              className="text-xs border-blue-400/50 text-blue-200 hover:bg-blue-500/30 hover:text-white"
              onClick={() => handleCreateNote(template)}
            >
              {template}
            </Button>
          ))}
        </div>
      </Card>
    </div>
  );
};

// Note Editor Component
interface NoteEditorProps {
  note: Note;
  onSave: (noteId: string, updates: Partial<Note>) => void;
  onCancel: () => void;
}

const NoteEditor: React.FC<NoteEditorProps> = ({ note, onSave, onCancel }) => {
  const [title, setTitle] = useState(note.title);
  const [content, setContent] = useState(note.content || '');
  const [folder, setFolder] = useState(note.folder);
  const [tags, setTags] = useState(note.tags.join(', '));
  const [dueDate, setDueDate] = useState(note.due_date ? note.due_date.split('T')[0] : '');

  const folders = ['Personal', 'Work', 'Ideas', 'Projects'];

  const handleSave = () => {
    const updates: Partial<Note> = {
      title: title.trim() || 'Untitled',
      content: content.trim(),
      folder,
      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      due_date: dueDate || undefined
    };

    onSave(note.id, updates);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">Edit Note</h1>
        <div className="flex items-center space-x-3">
          <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button onClick={onCancel} variant="outline" className="border-red-400/50 text-red-300 hover:bg-red-500/30">
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
        </div>
      </div>

      <Card className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 border border-blue-400/30">
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">Title</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              placeholder="Enter note title..."
            />
          </div>

          {/* Metadata Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Folder</label>
              <select
                value={folder}
                onChange={(e) => setFolder(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              >
                {folders.map(f => (
                  <option key={f} value={f} className="bg-blue-800">{f}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Tags (comma-separated)</label>
              <input
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                placeholder="tag1, tag2, tag3"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-200 mb-2">Due Date</label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent"
              />
            </div>
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-blue-200 mb-2">Content</label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={20}
              className="w-full px-4 py-3 bg-blue-600/30 border border-blue-400/50 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent resize-none"
              placeholder="Start writing your note..."
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Notes;