import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AuthPage from '../../src/app/auth/page'
import * as supabaseModule from '../../src/lib/supabase/client'

// Mock Supabase client
jest.mock('../../src/lib/supabase/client', () => ({
  supabase: {
    auth: {
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signInWithOAuth: jest.fn(),
    },
  },
  signInWithEmail: jest.fn(),
  signUpWithEmail: jest.fn(),
  signInWithGoogle: jest.fn(),
}))

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}))

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders authentication page with all options', () => {
    render(<AuthPage />)

    // Check for main elements
    expect(screen.getByText('Welcome to Koi')).toBeInTheDocument()
    expect(screen.getByText('Joyful Habits Hub')).toBeInTheDocument()
    
    // Check for authentication options
    expect(screen.getByText('Continue with Email')).toBeInTheDocument()
    expect(screen.getByText('Continue with Magic Link')).toBeInTheDocument()
    expect(screen.getByText('Continue with Google')).toBeInTheDocument()
  })

  it('handles email/password sign in flow', async () => {
    const user = userEvent.setup()
    
    ;(supabaseModule.signInWithEmail as jest.Mock).mockResolvedValue({
      data: { user: { id: 'test-user' } },
      error: null,
    })

    render(<AuthPage />)

    // Click email option
    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument()
    })

    // Fill in form
    const emailInput = screen.getByPlaceholderText('Enter your email')
    const passwordInput = screen.getByPlaceholderText('Enter your password')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')

    // Submit form
    const signInButton = screen.getByText('Sign In')
    fireEvent.click(signInButton)

    await waitFor(() => {
      expect(supabaseModule.signInWithEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'password123'
      )
    })
  })

  it('handles email/password sign up flow', async () => {
    const user = userEvent.setup()
    
    ;(supabaseModule.signUpWithEmail as jest.Mock).mockResolvedValue({
      data: { user: { id: 'test-user' } },
      error: null,
    })

    render(<AuthPage />)

    // Click email option
    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByText('Sign Up')).toBeInTheDocument()
    })

    // Switch to sign up
    const signUpTab = screen.getByText('Sign Up')
    fireEvent.click(signUpTab)

    // Fill in form
    const emailInput = screen.getByPlaceholderText('Enter your email')
    const passwordInput = screen.getByPlaceholderText('Create a password')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'newpassword123')

    // Submit form
    const signUpButton = screen.getByRole('button', { name: 'Sign Up' })
    fireEvent.click(signUpButton)

    await waitFor(() => {
      expect(supabaseModule.signUpWithEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'newpassword123'
      )
    })
  })

  it('handles Google OAuth sign in', async () => {
    ;(supabaseModule.signInWithGoogle as jest.Mock).mockResolvedValue({
      data: { url: 'https://oauth-url.com' },
      error: null,
    })

    render(<AuthPage />)

    const googleButton = screen.getByText('Continue with Google')
    fireEvent.click(googleButton)

    await waitFor(() => {
      expect(supabaseModule.signInWithGoogle).toHaveBeenCalled()
    })
  })

  it('handles magic link sign in', async () => {
    const user = userEvent.setup()
    
    ;(supabaseModule.supabase.auth.signInWithOAuth as jest.Mock).mockResolvedValue({
      data: {},
      error: null,
    })

    render(<AuthPage />)

    // Click magic link option
    const magicLinkButton = screen.getByText('Continue with Magic Link')
    fireEvent.click(magicLinkButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
    })

    // Fill in email
    const emailInput = screen.getByPlaceholderText('Enter your email')
    await user.type(emailInput, '<EMAIL>')

    // Submit
    const sendLinkButton = screen.getByText('Send Magic Link')
    fireEvent.click(sendLinkButton)

    await waitFor(() => {
      expect(screen.getByText(/Check your email/)).toBeInTheDocument()
    })
  })

  it('displays error messages for failed authentication', async () => {
    const user = userEvent.setup()
    
    ;(supabaseModule.signInWithEmail as jest.Mock).mockResolvedValue({
      data: null,
      error: { message: 'Invalid credentials' },
    })

    render(<AuthPage />)

    // Click email option and fill form
    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
    })

    const emailInput = screen.getByPlaceholderText('Enter your email')
    const passwordInput = screen.getByPlaceholderText('Enter your password')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'wrongpassword')

    const signInButton = screen.getByText('Sign In')
    fireEvent.click(signInButton)

    await waitFor(() => {
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    render(<AuthPage />)

    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
    })

    const emailInput = screen.getByPlaceholderText('Enter your email')
    await user.type(emailInput, 'invalid-email')

    const signInButton = screen.getByText('Sign In')
    fireEvent.click(signInButton)

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid email/)).toBeInTheDocument()
    })
  })

  it('validates password requirements', async () => {
    const user = userEvent.setup()
    render(<AuthPage />)

    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByText('Sign Up')).toBeInTheDocument()
    })

    const signUpTab = screen.getByText('Sign Up')
    fireEvent.click(signUpTab)

    const passwordInput = screen.getByPlaceholderText('Create a password')
    await user.type(passwordInput, '123') // Too short

    const signUpButton = screen.getByRole('button', { name: 'Sign Up' })
    fireEvent.click(signUpButton)

    // Should show validation error
    await waitFor(() => {
      expect(screen.getByText(/Password must be at least/)).toBeInTheDocument()
    })
  })

  it('shows loading states during authentication', async () => {
    const user = userEvent.setup()
    
    // Mock a delayed response
    ;(supabaseModule.signInWithEmail as jest.Mock).mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          data: { user: { id: 'test-user' } },
          error: null,
        }), 100)
      )
    )

    render(<AuthPage />)

    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
    })

    const emailInput = screen.getByPlaceholderText('Enter your email')
    const passwordInput = screen.getByPlaceholderText('Enter your password')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')

    const signInButton = screen.getByText('Sign In')
    fireEvent.click(signInButton)

    // Should show loading state
    expect(signInButton).toBeDisabled()
    expect(screen.getByText(/Signing in/)).toBeInTheDocument()
  })

  it('handles network errors gracefully', async () => {
    const user = userEvent.setup()
    
    ;(supabaseModule.signInWithEmail as jest.Mock).mockRejectedValue(
      new Error('Network error')
    )

    render(<AuthPage />)

    const emailButton = screen.getByText('Continue with Email')
    fireEvent.click(emailButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument()
    })

    const emailInput = screen.getByPlaceholderText('Enter your email')
    const passwordInput = screen.getByPlaceholderText('Enter your password')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')

    const signInButton = screen.getByText('Sign In')
    fireEvent.click(signInButton)

    await waitFor(() => {
      expect(screen.getByText(/Something went wrong/)).toBeInTheDocument()
    })
  })
})
